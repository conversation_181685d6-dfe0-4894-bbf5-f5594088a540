using MediatR;
using Microsoft.AspNetCore.Mvc;
using TmobCore.Cms.Application.Features.Blog.Commands.CreateBlog;
using TmobCore.Cms.Application.Features.Blog.Commands.DeleteBlog;
using TmobCore.Cms.Application.Features.Blog.Commands.UpdateBlog;
using TmobCore.Cms.Application.Features.Blog.Commands.UpdateBlogStatus;
using TmobCore.Cms.Application.Features.Blog.Queries.CheckBlogSlug;
using TmobCore.Cms.Application.Features.Blog.Queries.FindBlogBySlug;
using TmobCore.Cms.Application.Features.Blog.Queries.GetBlogById;
using TmobCore.Cms.Application.Features.Blog.Queries.GetBlogs;

namespace TmobCore.Cms.Api.Controllers.v1
{
    [Route("api/v1/[controller]")]
    [ApiController]
    public class BlogsController : ControllerBase
    {
        private readonly IMediator _mediator;
        public BlogsController(IMediator mediator)
        {
            _mediator = mediator;
        }

        [HttpPost]
        [ProducesResponseType(200)]
        public async Task<IActionResult> Get([FromBody] GetBlogsQuery query)
        {
            return Ok(await _mediator.Send(query));
        }

        [HttpGet("{Id}")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> Get(Guid Id)
        {
            var response = await _mediator.Send(new GetBlogByIdQuery { Id = Id });
            if (response.IsSuccessful)
            {
                return Ok(response);
            }
            return BadRequest(response);
        }

        [HttpPost("CreateBlog")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> CreateBlog([FromBody] CreateBlogCommand command)
        {
            var response = await _mediator.Send(command);
            if (response.IsSuccessful)
            {
                return Ok(response);
            }
            return BadRequest(response);
        }

        [HttpPut("UpdateBlogStatus")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> UpdateBlogStatus([FromBody] UpdateBlogStatusCommand command)
        {
            var response = await _mediator.Send(command);
            if (response.IsSuccessful)
            {
                return Ok(response);
            }
            return BadRequest(response);
        }

        [HttpPut]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> Put([FromBody] UpdateBlogCommand command)
        {
            var response = await _mediator.Send(command);
            if (response.IsSuccessful)
            {
                return Ok(response);
            }
            return BadRequest(response);
        }

        [HttpDelete]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> Delete([FromBody] DeleteBlogByIdCommand command)
        {
            var response = await _mediator.Send(command);
            if (response.IsSuccessful)
            {
                return Ok(response);
            }
            return BadRequest(response);
        }

        [HttpDelete("DeleteById")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> DeleteById([FromBody] DeleteBlogsByIdCommand command)
        {
            var response = await _mediator.Send(command);
            if (response.IsSuccessful)
            {
                return Ok(response);
            }
            return BadRequest(response);
        }

        [HttpPost("CheckSlug")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> CheckBlogSlug([FromBody] CheckBlogSlugQuery query)
        {
            var response = await _mediator.Send(query);
            if (response.IsSuccessful)
            {
                return Ok(response);
            }
            return BadRequest(response);
        }
        
        [HttpPost("FindBlogBySlug")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> FindBlogBySlug([FromBody] FindBlogBySlugQuery query)
        {
            var response = await _mediator.Send(query);
            if (response.IsSuccessful)
            {
                return Ok(response);
            }
            return BadRequest(response);
        } 
    }
}
