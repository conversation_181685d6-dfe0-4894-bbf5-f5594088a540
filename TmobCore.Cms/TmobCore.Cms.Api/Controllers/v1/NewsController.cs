using MediatR;
using Microsoft.AspNetCore.Mvc;
using TmobCore.Cms.Application.Features.News.Commands.CreateNews;
using TmobCore.Cms.Application.Features.News.Commands.DeleteNews;
using TmobCore.Cms.Application.Features.News.Commands.UpdateNews;
using TmobCore.Cms.Application.Features.News.Commands.UpdateNewsStatus;
using TmobCore.Cms.Application.Features.News.Queries.CheckNewSlug;
using TmobCore.Cms.Application.Features.News.Queries.GetNewById;
using TmobCore.Cms.Application.Features.News.Queries.GetNews;
using TmobCore.Cms.Application.Features.News.Queries.GetNewsBySlug;

namespace TmobCore.Cms.Api.Controllers.v1;

[Route("api/v1/[controller]")]
[ApiController]
public class NewsController : ControllerBase
{
    private readonly IMediator _mediator;
    
    public NewsController(IMediator mediator)
    {
        _mediator = mediator;
    }
    
    [HttpPost]
    [ProducesResponseType(200)]
    public async Task<IActionResult> Get([FromBody]GetNewsQuery query)
    {
        return Ok(await _mediator.Send(query));
    }
    
    [HttpGet("{Id}")]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    public async Task<IActionResult> Get(Guid Id)
    {
        var response = await _mediator.Send(new GetNewByIdQuery { Id = Id });
        if (response.IsSuccessful)
        {
            return Ok(response);
        }
        return BadRequest(response);
    }

    
    [HttpPost("CreateNews")]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    public async Task<IActionResult> CreateNews([FromBody] CreateNewsCommand command)
    {
        var response = await _mediator.Send(command);
        if (response.IsSuccessful)
        {
            return Ok(response);
        }
        return BadRequest(response);
    }

    [HttpPost("UpdateNewsStatus")]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    public async Task<IActionResult> UpdateNewsStatus([FromBody] UpdateNewsStatusCommand command)
    {
        var response = await _mediator.Send(command);
        if (response.IsSuccessful)
        {
            return Ok(response);
        }
        return BadRequest(response);
    }

    [HttpPut]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    public async Task<IActionResult> Put([FromBody] UpdateNewsCommand command)
    {
        var response = await _mediator.Send(command);
        if (response.IsSuccessful)
        {
            return Ok(response);
        }
        return BadRequest(response);
    }
    
    [HttpDelete]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    public async Task<IActionResult> Delete([FromBody] DeleteNewsByIdCommand command)
    {
        var response = await _mediator.Send(command);
        if (response.IsSuccessful)
        {
            return Ok(response);
        }
        return BadRequest(response);
    }

    [HttpDelete("DeleteById")]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    public async Task<IActionResult> DeleteById([FromBody] DeleteNewsItemsByIdCommand command)
    {
        var response = await _mediator.Send(command);
        if (response.IsSuccessful)
        {
            return Ok(response);
        }
        return BadRequest(response);
    }

    [HttpPost("CheckSlug")]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    public async Task<IActionResult> CheckNewSlug([FromBody] CheckNewSlugQuery query)
    {
        var response = await _mediator.Send(query);
        if (response.IsSuccessful)
        {
            return Ok(response);
        }
        return BadRequest(response);
    }
    
    [HttpGet("slug/{slug}")]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    public async Task<IActionResult> GetBySlug(string slug)
    {
        var response = await _mediator.Send(new GetNewsBySlugQuery { Slug = slug });
        if (response.IsSuccessful)
        {
            return Ok(response);
        }
        return BadRequest(response);
    }

}