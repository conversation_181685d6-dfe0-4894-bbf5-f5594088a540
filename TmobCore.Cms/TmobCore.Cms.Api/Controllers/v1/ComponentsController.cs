using MediatR;
using Microsoft.AspNetCore.Mvc;
using TmobCore.Cms.Application.Features.Component.Queries.GetComponents;
using TmobCore.Cms.Application.Features.Component.Queries.GetComponentsByPageId;

namespace TmobCore.Cms.Api.Controllers.v1
{
    [Route("api/v1/[controller]")]
    [ApiController]
    public class ComponentsController : ControllerBase
    {
        private readonly IMediator _mediator;

        public ComponentsController(IMediator mediator)
        {
            _mediator = mediator;
        }

        [HttpGet]
        [ProducesResponseType(200)]
        public async Task<IActionResult> Get()
        {
            return Ok(await _mediator.Send(new GetComponentsQuery()));
        }
        
        [HttpGet("{PageId}")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> Get(Guid PageId)
        {
            var response = await _mediator.Send(new GetComponentsByPageIdQuery { PageId = PageId });
            if (response.IsSuccessful)
            {
                return Ok(response);
            }
            return BadRequest(response);
        }
    }
}