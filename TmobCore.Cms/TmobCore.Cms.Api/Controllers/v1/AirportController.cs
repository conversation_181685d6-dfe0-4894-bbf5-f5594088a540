using MediatR;
using Microsoft.AspNetCore.Mvc;
using TmobCore.Cms.Application.Features.Airport.Commands.CreateAirport;
using TmobCore.Cms.Application.Features.Airport.Commands.DeleteAirport;
using TmobCore.Cms.Application.Features.Airport.Commands.UpdateAirport;
using TmobCore.Cms.Application.Features.Airport.Queries.GetAirportByCode;
using TmobCore.Cms.Application.Features.Airport.Queries.GetAirportById;
using TmobCore.Cms.Application.Features.Airport.Queries.GetAirports;
using TmobCore.Cms.Application.Features.Airport.Queries.GetAlternativeAirports;

namespace TmobCore.Cms.Api.Controllers.v1
{
    [Route("api/v1/[controller]")]
    [ApiController]
    public class AirportController : ControllerBase
    {
        private readonly IMediator _mediator;
        public AirportController(IMediator mediator)
        {
            _mediator = mediator;
        }

        [HttpPost]
        [ProducesResponseType(200)]
        public async Task<IActionResult> Get([FromBody] GetAirportsQuery query)
        {
            return Ok(await _mediator.Send(query));
        }

        [HttpGet("{Id}")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> Get(Guid Id)
        {
            var response = await _mediator.Send(new GetAirportByIdQuery { Id = Id });
            if (response.IsSuccessful)
            {
                return Ok(response);
            }
            return BadRequest(response);
        }

        /// <summary>
        /// Creates new airports with enhanced response including StatusCode and AlternativeAirports
        /// </summary>
        /// <param name="command">Airport creation command with list of airports</param>
        /// <returns>List of created airports with StatusCode and AlternativeAirports populated</returns>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST /api/v1/Airport/CreateAirport
        ///     {
        ///       "airports": [
        ///         {
        ///           "languageId": "11111111-1111-1111-1111-111111111111",
        ///           "groupId": "4ab5b0b9-e4e9-4e61-8329-c09a5799abfc",
        ///           "code": "JFK",
        ///           "name": "John F. Kennedy International Airport",
        ///           "latitude": 40.6413,
        ///           "longitude": -73.7781,
        ///           "alternativeAirports": "LGA,EWR",
        ///           "order": 1,
        ///           "cityId": "22222222-2222-2222-2222-222222222222",
        ///           "slug": "jfk-airport"
        ///         }
        ///       ]
        ///     }
        ///
        /// Sample response includes StatusCode (201) and AlternativeAirports array for each created airport.
        /// </remarks>
        [HttpPost("CreateAirport")]
        [ProducesResponseType(201)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> CreateAirport([FromBody] CreateAirportCommand command)
        {
            var response = await _mediator.Send(command);
            if (response.IsSuccessful)
            {
                return StatusCode(201, response);
            }
            return BadRequest(response);
        }

        [HttpPut]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> Put([FromBody] UpdateAirportCommand command)
        {
            var response = await _mediator.Send(command);
            if (response.IsSuccessful)
            {
                return Ok(response);
            }
            return BadRequest(response);
        }

        [HttpDelete]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> Delete([FromBody] DeleteAirportByIdCommand command)
        {
            var response = await _mediator.Send(command);
            if (response.IsSuccessful)
            {
                return Ok(response);
            }
            return BadRequest(response);
        }

        [HttpPost("GetAlternativeAirportsByCode")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> GetAlternativeAirportsByCode([FromBody] GetAlternativeAirportsByCodeQuery query)
        {
            var response = await _mediator.Send(query);
            if (response.IsSuccessful)
            {
                return Ok(response);
            }
            return BadRequest(response);
        }

        [HttpGet]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> Get([FromQuery] string Code)
        {
            var response = await _mediator.Send(new GetAirportByCodeQuery { Code = Code });
            if (response.IsSuccessful)
            {
                return Ok(response);
            }
            return BadRequest(response);
        }
    }
}
