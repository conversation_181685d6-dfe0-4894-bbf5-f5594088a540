using MediatR;
using Microsoft.AspNetCore.Mvc;
using TmobCore.Cms.Application.Features.Blog.Commands.CreateBlogFilter;
using TmobCore.Cms.Application.Features.Blog.Commands.DeleteBlogFilter;
using TmobCore.Cms.Application.Features.Blog.Commands.UpdateBlog;
using TmobCore.Cms.Application.Features.Blog.Queries.FindBlogFilterBySlug;
using TmobCore.Cms.Application.Features.Blog.Queries.GetBlogFilters;
using TmobCore.Cms.Application.Features.Blog.Queries.GetBlogFiltersById;

namespace TmobCore.Cms.Api.Controllers.v1;

[Route("api/v1/[controller]")]
[ApiController]
public class BlogFiltersController : ControllerBase
{
    private readonly IMediator _mediator;

    public BlogFiltersController(IMediator mediator)
    {
        _mediator = mediator;
    }

    [HttpPost]
    [ProducesResponseType(200)]
    public async Task<IActionResult> GetBlogFilters([FromBody] GetBlogFiltersQuery query)
    {
        return Ok(await _mediator.Send(query));
    }

    [HttpGet("{Id}")]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    public async Task<IActionResult> Get(Guid Id)
    {
        var response = await _mediator.Send(new GetBlogFiltersByIdQuery { Id = Id });
        if (response.IsSuccessful)
        {
            return Ok(response);
        }
        return BadRequest(response);
    }

    [HttpPost("CreateBlogFilter")]
    [ProducesResponseType(201)]
    [ProducesResponseType(400)]
    public async Task<IActionResult> Create([FromBody] CreateBlogFilterCommand command)
    {
        var response = await _mediator.Send(command);
        if (response.IsSuccessful) return Ok(response);
        return BadRequest(response);
    }

    [HttpPut]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    public async Task<IActionResult> Put([FromBody] UpdateBlogFilterCommand command)
    {
        var response = await _mediator.Send(command);
        if (response.IsSuccessful)
        {
            return Ok(response);
        }
        return BadRequest(response);
    }

    [HttpDelete]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    public async Task<IActionResult> Delete([FromBody] DeleteBlogFilterCommand command)
    {
        var response = await _mediator.Send(command);
        if (response.IsSuccessful)
        {
            return Ok(response);
        }
        return BadRequest(response);
    }
    
    [HttpPost("FindBlogFilterBySlug")]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    public async Task<IActionResult> FindBlogFilterBySlug([FromBody] FindBlogFilterBySlugQuery query)
    {
        var response = await _mediator.Send(query);
        if (response.IsSuccessful)
        {
            return Ok(response);
        }
        return BadRequest(response);
    }

    [HttpDelete("DeleteById")]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    public async Task<IActionResult> DeleteById([FromBody] DeleteBlogFiltersByIdCommand command)
    {
        var response = await _mediator.Send(command);
        if (response.IsSuccessful)
        {
            return Ok(response);
        }
        return BadRequest(response);
    }
}