using MediatR;
using Microsoft.AspNetCore.Mvc;
using TmobCore.Cms.Application.Features.Insight.Commands.CreateInsight;
using TmobCore.Cms.Application.Features.Insight.Commands.DeleteInsight;
using TmobCore.Cms.Application.Features.Insight.Commands.UpdateInsight;
using TmobCore.Cms.Application.Features.Insight.Commands.UpdateInsightStatus;
using TmobCore.Cms.Application.Features.Insight.Queries.CheckInsightSlug;
using TmobCore.Cms.Application.Features.Insight.Queries.GetInsightById;
using TmobCore.Cms.Application.Features.Insight.Queries.GetInsights;

namespace TmobCore.Cms.Api.Controllers.v1
{
    [Route("api/v1/[controller]")]
    [ApiController]
    public class InsightsController : ControllerBase
    {
        private readonly IMediator _mediator;
        public InsightsController(IMediator mediator)
        {
            _mediator = mediator;
        }

        [HttpPost]
        [ProducesResponseType(200)]
        public async Task<IActionResult> Get([FromBody] GetInsightsQuery query)
        {
            return Ok(await _mediator.Send(query));
        }

        [HttpGet("{GroupId}")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> Get(Guid GroupId)
        {
            var response = await _mediator.Send(new GetInsightByIdQuery { GroupId = GroupId });
            if (response.IsSuccessful)
            {
                return Ok(response);
            }
            return BadRequest(response);
        }

        [HttpPost("CreateInsight")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> CreateInsight([FromBody] CreateInsightCommand command)
        {
            var response = await _mediator.Send(command);
            if (response.IsSuccessful)
            {
                return Ok(response);
            }
            return BadRequest(response);
        }

        [HttpPut("UpdateInsightStatus")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> UpdateInsightStatus([FromBody] UpdateInsightStatusCommand command)
        {
            var response = await _mediator.Send(command);
            if (response.IsSuccessful)
            {
                return Ok(response);
            }
            return BadRequest(response);
        }

        [HttpPut]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> Put([FromBody] UpdateInsightCommand command)
        {
            var response = await _mediator.Send(command);
            if (response.IsSuccessful)
            {
                return Ok(response);
            }
            return BadRequest(response);
        }

        [HttpDelete]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> Delete([FromBody] DeleteInsightByIdCommand command)
        {
            var response = await _mediator.Send(command);
            if (response.IsSuccessful)
            {
                return Ok(response);
            }
            return BadRequest(response);
        }

        [HttpDelete("DeleteById")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> DeleteById([FromBody] DeleteInsightsByIdCommand command)
        {
            var response = await _mediator.Send(command);
            if (response.IsSuccessful)
            {
                return Ok(response);
            }
            return BadRequest(response);
        }

        [HttpPost("CheckSlug")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> CheckSlug([FromBody] CheckInsightSlugQuery query)
        {
            var response = await _mediator.Send(query);
            if (response.IsSuccessful)
            {
                return Ok(response);
            }
            return BadRequest(response);
        }
    }
}
