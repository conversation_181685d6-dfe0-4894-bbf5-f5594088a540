using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace TmobCore.Cms.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddGroupIdToBlogFilter : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "GroupId",
                table: "BlogFilters",
                type: "uniqueidentifier",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "GroupId",
                table: "BlogFilters");

        }
    }
}
