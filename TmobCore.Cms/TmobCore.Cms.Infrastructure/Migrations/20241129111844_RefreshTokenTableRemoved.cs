using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace TmobCore.Cms.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class RefreshTokenTableRemoved : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "RefreshTokens");

            migrationBuilder.DeleteData(
                table: "Example",
                keyColumn: "Id",
                keyValue: new Guid("70c285ae-a1ec-43c7-aa4e-cdbd280e64f7"));

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "302dffb7-ba4e-44e2-b77a-1b258954f9d5",
                columns: new[] { "ConcurrencyStamp", "PasswordHash", "SecurityStamp" },
                values: new object[] { "c123874c-a23d-4082-b43b-ee37fec8cc7d", "AQAAAAIAAYagAAAAEEq5G9fbN81X/Wt3vtTr28M1AOB81apKWfaE8qcCPGOSzCjW9ZrzusNVCMlWsrdg5g==", "d009830f-8257-4d2c-8743-27c26b4de2fc" });

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "b28b07da-03a3-435a-a370-2a675c11a57d",
                columns: new[] { "ConcurrencyStamp", "PasswordHash", "SecurityStamp" },
                values: new object[] { "c6643b55-478d-46fb-99eb-1a1799b9c5fa", "AQAAAAIAAYagAAAAELqfmqvte+ID7Ue+JAG1nsibM3+mae6hTO2ujnPpzuM64F5cDNCiG50/KniXKGiH4A==", "9f4bf05d-4115-4a57-8ac5-22923f4df719" });

            migrationBuilder.InsertData(
                table: "Example",
                columns: new[] { "Id", "Count", "DateCreated", "DateModified", "Description", "LanguageId", "Name" },
                values: new object[] { new Guid("8ce47b57-45f0-4c4b-8194-5ead91d8decc"), 13, new DateTime(2024, 11, 29, 14, 18, 43, 881, DateTimeKind.Local).AddTicks(4990), new DateTime(2024, 11, 29, 14, 18, 43, 881, DateTimeKind.Local).AddTicks(5030), "The modelBuilder.Entity<Example>().HasData() method in Entity Framework is used to seed initial data into the database for a specific entity (in this case, the \"Example\" entity).", null, "First Data" });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "Example",
                keyColumn: "Id",
                keyValue: new Guid("8ce47b57-45f0-4c4b-8194-5ead91d8decc"));

            migrationBuilder.CreateTable(
                name: "RefreshTokens",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    UserId = table.Column<string>(type: "nvarchar(450)", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    ExpireTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    IsRevoked = table.Column<bool>(type: "bit", nullable: false),
                    RevokedAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    Token = table.Column<string>(type: "nvarchar(max)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_RefreshTokens", x => x.Id);
                    table.ForeignKey(
                        name: "FK_RefreshTokens_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "302dffb7-ba4e-44e2-b77a-1b258954f9d5",
                columns: new[] { "ConcurrencyStamp", "PasswordHash", "SecurityStamp" },
                values: new object[] { "c83374c2-eff3-48a3-b8ce-f580fed3cb28", "AQAAAAIAAYagAAAAEFJ6CU+FE+SHZyyU/JeMPFBuPAiPmGmEK59EXCWJXm4vdoBDtPw7VqyiclVEy5IWTA==", "53c4c979-76be-4daa-b0e4-6aa524a41152" });

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "b28b07da-03a3-435a-a370-2a675c11a57d",
                columns: new[] { "ConcurrencyStamp", "PasswordHash", "SecurityStamp" },
                values: new object[] { "147b07ff-a898-487f-acf1-31bee31f39ff", "AQAAAAIAAYagAAAAENpqAOtWgzK9WSaqBBXeTIkQ4Hd6Ai483EO9uvn5+2obkuQtRQYsggyixu0Fi6s2GA==", "0043b0d1-7617-4deb-b056-d6acc94b364a" });

            migrationBuilder.InsertData(
                table: "Example",
                columns: new[] { "Id", "Count", "DateCreated", "DateModified", "Description", "LanguageId", "Name" },
                values: new object[] { new Guid("70c285ae-a1ec-43c7-aa4e-cdbd280e64f7"), 13, new DateTime(2024, 11, 28, 23, 58, 57, 147, DateTimeKind.Local).AddTicks(4800), new DateTime(2024, 11, 28, 23, 58, 57, 147, DateTimeKind.Local).AddTicks(4850), "The modelBuilder.Entity<Example>().HasData() method in Entity Framework is used to seed initial data into the database for a specific entity (in this case, the \"Example\" entity).", null, "First Data" });

            migrationBuilder.CreateIndex(
                name: "IX_RefreshTokens_UserId",
                table: "RefreshTokens",
                column: "UserId");
        }
    }
}
