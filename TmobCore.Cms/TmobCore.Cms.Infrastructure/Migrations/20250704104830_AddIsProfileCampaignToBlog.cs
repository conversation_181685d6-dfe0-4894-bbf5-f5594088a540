using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace TmobCore.Cms.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddIsProfileCampaignToBlog : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "Example",
                keyColumn: "Id",
                keyValue: new Guid("2772550e-2f4a-4b29-8b5a-cdd133527e8c"));

            migrationBuilder.AddColumn<bool>(
                name: "IsProfileCampaign",
                table: "Blogs",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "302dffb7-ba4e-44e2-b77a-1b258954f9d5",
                columns: new[] { "ConcurrencyStamp", "PasswordHash", "SecurityStamp" },
                values: new object[] { "3c58e35b-3250-4b39-8491-54fc91f73c84", "AQAAAAIAAYagAAAAECt901J41raV/wig63SGwN0Cra9+IbRVeP/wXJ2N6+Znw8utCEKY8yU1gudciLWhag==", "e50c992e-06bd-42ed-8034-245dc6c60b2b" });

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "b28b07da-03a3-435a-a370-2a675c11a57d",
                columns: new[] { "ConcurrencyStamp", "PasswordHash", "SecurityStamp" },
                values: new object[] { "2ec39fbb-9a24-4dcd-b61e-e07ade692a92", "AQAAAAIAAYagAAAAEKoYGLJmiTPL8L2fSgrc6GZNOQiVxYklKrpH7tnj6w91B6LGqaQ7v/1EbkspFyuhLQ==", "2350b745-df89-44ca-8db3-01cfdcdfa9a6" });

            migrationBuilder.InsertData(
                table: "Example",
                columns: new[] { "Id", "Count", "DateCreated", "DateModified", "Description", "LanguageId", "Name" },
                values: new object[] { new Guid("ba2db1e8-93d9-43ca-a94e-c5f2eaa92b9b"), 13, new DateTime(2025, 7, 4, 13, 48, 29, 979, DateTimeKind.Local).AddTicks(5810), new DateTime(2025, 7, 4, 13, 48, 29, 979, DateTimeKind.Local).AddTicks(5860), "The modelBuilder.Entity<Example>().HasData() method in Entity Framework is used to seed initial data into the database for a specific entity (in this case, the \"Example\" entity).", null, "First Data" });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "Example",
                keyColumn: "Id",
                keyValue: new Guid("ba2db1e8-93d9-43ca-a94e-c5f2eaa92b9b"));

            migrationBuilder.DropColumn(
                name: "IsProfileCampaign",
                table: "Blogs");

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "302dffb7-ba4e-44e2-b77a-1b258954f9d5",
                columns: new[] { "ConcurrencyStamp", "PasswordHash", "SecurityStamp" },
                values: new object[] { "a3220736-f5f2-4796-b228-f59226657754", "AQAAAAIAAYagAAAAENzSXWUq5dEl1mq4C0Od5SDx6P75ZpKAZETbju61a4Gnt5OET6hQxRsDTCDYvrudSg==", "932090da-346d-4acd-bf4a-81d109147c19" });

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "b28b07da-03a3-435a-a370-2a675c11a57d",
                columns: new[] { "ConcurrencyStamp", "PasswordHash", "SecurityStamp" },
                values: new object[] { "94f73e81-51f8-495e-9f6b-9caeb1a986b1", "AQAAAAIAAYagAAAAEOYlk8vk6c650eod7lwFw/kwsZHpE53KerbGfWxowREAdu8w6Hf6tgKhCn79/GEdPw==", "65b0f62b-54d2-4fa7-a30b-2fcde0ea5d38" });

            migrationBuilder.InsertData(
                table: "Example",
                columns: new[] { "Id", "Count", "DateCreated", "DateModified", "Description", "LanguageId", "Name" },
                values: new object[] { new Guid("2772550e-2f4a-4b29-8b5a-cdd133527e8c"), 13, new DateTime(2025, 7, 3, 16, 51, 38, 326, DateTimeKind.Local).AddTicks(290), new DateTime(2025, 7, 3, 16, 51, 38, 326, DateTimeKind.Local).AddTicks(340), "The modelBuilder.Entity<Example>().HasData() method in Entity Framework is used to seed initial data into the database for a specific entity (in this case, the \"Example\" entity).", null, "First Data" });
        }
    }
}
