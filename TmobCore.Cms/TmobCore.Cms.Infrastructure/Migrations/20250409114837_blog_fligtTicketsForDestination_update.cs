using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace TmobCore.Cms.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class blog_fligtTicketsForDestination_update : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "Example",
                keyColumn: "Id",
                keyValue: new Guid("81c4a9c4-abae-4408-8037-e47eca6aed19"));

            migrationBuilder.AddColumn<string>(
                name: "AirportCode",
                table: "Blogs",
                type: "nvarchar(8)",
                maxLength: 8,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "CardDescription",
                table: "Blogs",
                type: "nvarchar(400)",
                maxLength: 400,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "<PERSON>Tit<PERSON>",
                table: "Blogs",
                type: "nvarchar(150)",
                maxLength: 150,
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "Visible",
                table: "Blogs",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "302dffb7-ba4e-44e2-b77a-1b258954f9d5",
                columns: new[] { "ConcurrencyStamp", "PasswordHash", "SecurityStamp" },
                values: new object[] { "d88068d5-271f-4edf-8a12-8d0ce1751c37", "AQAAAAIAAYagAAAAEN1c2OgLMtIt0ShXzAYN63IJgJG4ZYAKU+hGbDeMvsceZaaADsMYwxQhLHbi4puVGg==", "8752713c-3fc5-4473-b337-0ab6baa6a0f3" });

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "b28b07da-03a3-435a-a370-2a675c11a57d",
                columns: new[] { "ConcurrencyStamp", "PasswordHash", "SecurityStamp" },
                values: new object[] { "fcb1118c-7daa-4149-8cb0-31e6af050791", "AQAAAAIAAYagAAAAEA3qY/j0hxW5L2zNe1f8RK+gFdn4siHdYfGWDSJUFeO0taIy/DhQaBJXc4eKPj2QTw==", "f37a9113-e8b6-47c4-bd77-3e0d4baf7566" });

            migrationBuilder.InsertData(
                table: "Example",
                columns: new[] { "Id", "Count", "DateCreated", "DateModified", "Description", "LanguageId", "Name" },
                values: new object[] { new Guid("b5515224-ba5c-44d3-a7c0-bc8c02786951"), 13, new DateTime(2025, 4, 9, 14, 48, 37, 537, DateTimeKind.Local).AddTicks(9180), new DateTime(2025, 4, 9, 14, 48, 37, 537, DateTimeKind.Local).AddTicks(9220), "The modelBuilder.Entity<Example>().HasData() method in Entity Framework is used to seed initial data into the database for a specific entity (in this case, the \"Example\" entity).", null, "First Data" });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "Example",
                keyColumn: "Id",
                keyValue: new Guid("b5515224-ba5c-44d3-a7c0-bc8c02786951"));

            migrationBuilder.DropColumn(
                name: "AirportCode",
                table: "Blogs");

            migrationBuilder.DropColumn(
                name: "CardDescription",
                table: "Blogs");

            migrationBuilder.DropColumn(
                name: "CardTitle",
                table: "Blogs");

            migrationBuilder.DropColumn(
                name: "Visible",
                table: "Blogs");

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "302dffb7-ba4e-44e2-b77a-1b258954f9d5",
                columns: new[] { "ConcurrencyStamp", "PasswordHash", "SecurityStamp" },
                values: new object[] { "64fb6e93-279f-44ae-9342-9ca1b1554c96", "AQAAAAIAAYagAAAAEFrAeiDuCSoXt66RBFtyU+4uc8n1tbg2ymR8nXQtZWNCn8MNDSfrmjtbupYnBDz/+w==", "1591998d-7c7e-4264-bae8-93a0d692fcbf" });

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "b28b07da-03a3-435a-a370-2a675c11a57d",
                columns: new[] { "ConcurrencyStamp", "PasswordHash", "SecurityStamp" },
                values: new object[] { "657ed63d-b37f-4e72-9abe-3605f06294dc", "AQAAAAIAAYagAAAAEJgmSdyzbilgpQDiV2sxlRHvFiVC1rPbHv3mFmBqSl8c0KC+tOFClBFhU5hkhxFaeQ==", "ad65940c-5427-49a7-a1e9-dd6d6fb0a13a" });

            migrationBuilder.InsertData(
                table: "Example",
                columns: new[] { "Id", "Count", "DateCreated", "DateModified", "Description", "LanguageId", "Name" },
                values: new object[] { new Guid("81c4a9c4-abae-4408-8037-e47eca6aed19"), 13, new DateTime(2025, 4, 7, 10, 34, 17, 382, DateTimeKind.Local).AddTicks(7600), new DateTime(2025, 4, 7, 10, 34, 17, 382, DateTimeKind.Local).AddTicks(7640), "The modelBuilder.Entity<Example>().HasData() method in Entity Framework is used to seed initial data into the database for a specific entity (in this case, the \"Example\" entity).", null, "First Data" });
        }
    }
}
