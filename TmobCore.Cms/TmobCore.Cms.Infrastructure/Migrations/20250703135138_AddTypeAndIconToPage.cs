using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace TmobCore.Cms.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddTypeAndIconToPage : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "Example",
                keyColumn: "Id",
                keyValue: new Guid("c9d2845c-c5d1-406b-b4c7-d43974ffae64"));

            migrationBuilder.AddColumn<string>(
                name: "Icon",
                table: "Pages",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "Type",
                table: "Pages",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "302dffb7-ba4e-44e2-b77a-1b258954f9d5",
                columns: new[] { "ConcurrencyStamp", "PasswordHash", "SecurityStamp" },
                values: new object[] { "a3220736-f5f2-4796-b228-f59226657754", "AQAAAAIAAYagAAAAENzSXWUq5dEl1mq4C0Od5SDx6P75ZpKAZETbju61a4Gnt5OET6hQxRsDTCDYvrudSg==", "932090da-346d-4acd-bf4a-81d109147c19" });

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "b28b07da-03a3-435a-a370-2a675c11a57d",
                columns: new[] { "ConcurrencyStamp", "PasswordHash", "SecurityStamp" },
                values: new object[] { "94f73e81-51f8-495e-9f6b-9caeb1a986b1", "AQAAAAIAAYagAAAAEOYlk8vk6c650eod7lwFw/kwsZHpE53KerbGfWxowREAdu8w6Hf6tgKhCn79/GEdPw==", "65b0f62b-54d2-4fa7-a30b-2fcde0ea5d38" });

            migrationBuilder.InsertData(
                table: "Example",
                columns: new[] { "Id", "Count", "DateCreated", "DateModified", "Description", "LanguageId", "Name" },
                values: new object[] { new Guid("2772550e-2f4a-4b29-8b5a-cdd133527e8c"), 13, new DateTime(2025, 7, 3, 16, 51, 38, 326, DateTimeKind.Local).AddTicks(290), new DateTime(2025, 7, 3, 16, 51, 38, 326, DateTimeKind.Local).AddTicks(340), "The modelBuilder.Entity<Example>().HasData() method in Entity Framework is used to seed initial data into the database for a specific entity (in this case, the \"Example\" entity).", null, "First Data" });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "Example",
                keyColumn: "Id",
                keyValue: new Guid("2772550e-2f4a-4b29-8b5a-cdd133527e8c"));

            migrationBuilder.DropColumn(
                name: "Icon",
                table: "Pages");

            migrationBuilder.DropColumn(
                name: "Type",
                table: "Pages");

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "302dffb7-ba4e-44e2-b77a-1b258954f9d5",
                columns: new[] { "ConcurrencyStamp", "PasswordHash", "SecurityStamp" },
                values: new object[] { "088af678-e85c-4d34-9f9f-063d1c614179", "AQAAAAIAAYagAAAAECj+yXBh3V4tZ8dNcv4rP6nwOdCNyzvSdMJiBRP8jrbEwUTdTigW9NgrV+15ynWZPA==", "757f402a-215b-4afa-bd39-88acbd0b80c9" });

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "b28b07da-03a3-435a-a370-2a675c11a57d",
                columns: new[] { "ConcurrencyStamp", "PasswordHash", "SecurityStamp" },
                values: new object[] { "93b0ea7b-1648-42a8-aa81-715cae12d1c0", "AQAAAAIAAYagAAAAEFExD1WBA9TGOlJ3sSKizd/nT3jg6lwBvM99oeViA4GJqnyxV0FM2uupMmD5tqpcUQ==", "b5982acd-390b-4003-b2d7-f3f2086323d9" });

            migrationBuilder.InsertData(
                table: "Example",
                columns: new[] { "Id", "Count", "DateCreated", "DateModified", "Description", "LanguageId", "Name" },
                values: new object[] { new Guid("c9d2845c-c5d1-406b-b4c7-d43974ffae64"), 13, new DateTime(2025, 6, 10, 9, 17, 31, 760, DateTimeKind.Local).AddTicks(1069), new DateTime(2025, 6, 10, 9, 17, 31, 760, DateTimeKind.Local).AddTicks(1086), "The modelBuilder.Entity<Example>().HasData() method in Entity Framework is used to seed initial data into the database for a specific entity (in this case, the \"Example\" entity).", null, "First Data" });
        }
    }
}
