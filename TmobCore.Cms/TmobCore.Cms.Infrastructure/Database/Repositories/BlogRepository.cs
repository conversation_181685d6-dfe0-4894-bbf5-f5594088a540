using Microsoft.EntityFrameworkCore;
using TmobCore.Cms.Application.Contracts.Persistence;
using TmobCore.Cms.Application.Models.Blog;
using TmobCore.Cms.Domain.Entities;
using TmobCore.Cms.Infrastructure.Database.DatabaseContext;

namespace TmobCore.Cms.Infrastructure.Database.Repositories
{
    public class BlogRepository : GenericRepository<Blog>, IBlogRepository
    {
        public BlogRepository(CmsCoreDatabaseContext context) : base(context)
        {
        }

        public async Task<bool> IsSlugUnique(string slug, Guid? id = null)
        {
            var query = _context.Blogs.AsQueryable();
            if (id.HasValue)
                query = query.Where(x => x.Id != id);

            return !await query.AnyAsync(x => x.Slug == slug && !x.Deleted);
        }

        public async Task<(List<Blog> Blogs, int TotalCount)> GetBlogs(BlogRequest request, CancellationToken cancellationToken)
        {
            var query = _context.Blogs
                .AsNoTracking()
                .Include(x => x.BlogContents)
                .ThenInclude(bc => bc.Image)
                .Include(x => x.Image)
                .Include(x => x.BlogBlogFilters)
                .ThenInclude(y => y.BlogFilter)
                .Where(x => !x.Deleted);

            if (request.ProjectId != null)
                query = query.Where(x => x.ProjectId == request.ProjectId);

            if (request.GroupId.HasValue)
                query = query.Where(x => x.GroupId == request.GroupId);

            if (request.IsProfileCampaign.HasValue)
                query = query.Where(x => x.IsProfileCampaign == request.IsProfileCampaign.Value);

            if (!string.IsNullOrEmpty(request.SearchTerm))
            {
                query = query.Where(x =>
                    x.Title.Contains(request.SearchTerm) ||
                    x.BlogContents.Any(bc => request.SearchTerm.Contains(bc.Content)));
            }

            // Veritabanından tüm eşleşen blogları al
            var allBlogs = await query.ToListAsync(cancellationToken);

            // FilterIds varsa, bellek tarafında filtrele öncesinde query e ekleyerek filtrelemeye çalıştığımda hata aldım
            if (request.FilterIds != null && request.FilterIds.Any())
            {
                var filterIdsSet = new HashSet<Guid>(request.FilterIds);
                allBlogs = allBlogs
                    .Where(blog => blog.BlogBlogFilters
                        .Any(bbf => filterIdsSet.Contains(bbf.BlogFilterId)))
                    .ToList();
            }

            // BlogContents sıralaması
            foreach (var blog in allBlogs)
            {
                blog.BlogContents = blog.BlogContents.OrderBy(bc => bc.Order).ToList();
            }

            var blogs = allBlogs
                .GroupBy(x => x.GroupId)
                .SelectMany(group => group)
                .ToList();

            var totalCount = blogs.Count;
            var response = blogs
                .Skip((request.Page - 1) * request.PageSize)
                .Take(request.PageSize)
                .ToList();

            return (response, totalCount);
        }


        public async Task<Blog> GetBlogByIdAsync(Guid? blogId, CancellationToken cancellationToken)
        {
            var blog = await _context.Blogs
                .AsNoTracking()
                .Include(x => x.BlogContents)
                .ThenInclude(bc => bc.Image)
                .Include(x => x.Image)
                .Include(x => x.BlogBlogFilters)
                .ThenInclude(y => y.BlogFilter)
                .Where(x => x.Id == blogId && !x.Deleted)
                .FirstOrDefaultAsync(cancellationToken);

            return blog;
        }

        public async Task<List<Blog>> GetBlogByGroupIdAsync(Guid groupId)
        {
            var blogs = await _context.Blogs
                .Where(x => x.GroupId == groupId && !x.Deleted)
                .Include(ic => ic.BlogContents
                    .Where(c => !c.Deleted)
                    .OrderBy(c => c.Order)
                    .ThenBy(c => c.DateCreated))
                .Include(x => x.Image)
                .Include(x => x.BlogBlogFilters)
                .ThenInclude(y => y.BlogFilter)
                .ToListAsync();
            return blogs;
        }
    }
}
