using Microsoft.EntityFrameworkCore;
using TmobCore.Cms.Application.Contracts.Persistence;
using TmobCore.Cms.Domain.Entities;
using TmobCore.Cms.Infrastructure.Database.DatabaseContext;

namespace TmobCore.Cms.Infrastructure.Database.Repositories
{
    public class AirportRepository : GenericRepository<Airport>, IAirportRepository
    {
        public AirportRepository(CmsCoreDatabaseContext context) : base(context)
        {
        }

        public async Task<List<string>> GetAlternativeAirportsByCode(string airportCode)
        {
            var airports = _context.Airports.FirstOrDefault(x => x.Code == airportCode)?.AlternativeAirports;
            List<string> airportList = new();
            if (!String.IsNullOrEmpty(airports))
            {
                airportList = airports.Split(',').ToList();
            }

            return airportList;
        }

        public async Task<List<Airport>> GetAlternativeAirportObjectsByCode(string airportCode)
        {
            try
            {
                // First, get the airport to find its alternative airports string
                var alternativeAirportsString = await _context.Airports
                    .AsNoTracking()
                    .Where(x => x.Code == airportCode && x.IsActive && !x.IsDeleted)
                    .Select(x => x.AlternativeAirports)
                    .FirstOrDefaultAsync();

                if (string.IsNullOrEmpty(alternativeAirportsString))
                {
                    return new List<Airport>();
                }

                // Split the comma-separated alternative airport codes
                var alternativeCodes = alternativeAirportsString
                    .Split(',', StringSplitOptions.RemoveEmptyEntries)
                    .Select(code => code.Trim())
                    .Where(code => !string.IsNullOrEmpty(code))
                    .ToList();

                if (!alternativeCodes.Any())
                {
                    return new List<Airport>();
                }

                // Fetch alternative airports one by one to avoid complex SQL
                var alternativeAirports = new List<Airport>();

                foreach (var code in alternativeCodes)
                {
                    // Get airport with city
                    var airport = await _context.Airports
                        .AsNoTracking()
                        .Include(a => a.City)
                        .Where(a => a.Code == code && a.IsActive && !a.IsDeleted)
                        .FirstOrDefaultAsync();

                    if (airport?.City != null)
                    {
                        // Get country separately
                        var country = await _context.Countries
                            .AsNoTracking()
                            .Where(c => c.Id == airport.City.CountryId)
                            .FirstOrDefaultAsync();

                        if (country != null)
                        {
                            airport.City.Country = country;
                        }

                        alternativeAirports.Add(airport);
                    }
                    else if (airport != null)
                    {
                        // Add airport even without city/country data
                        alternativeAirports.Add(airport);
                    }
                }

                return alternativeAirports;
            }
            catch (Exception)
            {
                // Return empty list if there's any database error
                return new List<Airport>();
            }
        }
    }
}