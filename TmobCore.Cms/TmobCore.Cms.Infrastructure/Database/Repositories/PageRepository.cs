using AutoMapper;
using Microsoft.EntityFrameworkCore;
using TmobCore.Cms.Application.Contracts.Persistence;
using TmobCore.Cms.Application.Models.Common;
using TmobCore.Cms.Application.Models.Image;
using TmobCore.Cms.Application.Models.Page;
using TmobCore.Cms.Domain.Entities;
using TmobCore.Cms.Infrastructure.Database.DatabaseContext;

namespace TmobCore.Cms.Infrastructure.Database.Repositories
{
    public class PageRepository : GenericRepository<Page>, IPageRepository
    {
        private readonly CmsCoreDatabaseContext _context;
        private readonly IMapper _mapper;
        public PageRepository(CmsCoreDatabaseContext context, IMapper mapper) : base(context)
        {
            _context = context;
            _mapper = mapper;
        }

        public async Task<bool> IsSlugUnique(string slug, Guid? id = null)
        {
            var query = _context.Pages.AsQueryable();
            if (id.HasValue)
                query = query.Where(x => x.Id != id);

            return !await query.AnyAsync(x => x.Slug == slug && !x.Deleted);
        }

        public List<PageResponse> RecursiveSubPages(Guid? parentId, CancellationToken cancellationToken)
        {
            List<PageResponse> data = new();
            var subPages = GetQuery(x => x.ParentPageId == parentId && !x.Deleted).ToListAsync(cancellationToken).Result;
            if (subPages.Any())
            {
                subPages.ForEach((subPage) =>
                {
                    // var subPageContents = _context.PageContents
                    //     .Where(x => x.PageId == subPage.Id && !x.Deleted)?
                    //     .Select(y => new PageContentResponse 
                    //     {
                    //         Id = y.Id, 
                    //         Content = y.Content, 
                    //         Order = y.Order
                    //     })?
                    //     .ToList();
                    
                    data.Add(new PageResponse
                    {
                        Id = subPage.Id,
                        Title  = subPage.Title,
                        Slug = subPage.Slug,
                        Type = subPage.Type,
                        Icon = subPage.Icon,
                        Order = subPage.Order,
                        File = _mapper.Map<ImageResponse>(subPage.Image),
                        PublishDate = subPage.PublishDate,
                        // PageContents = subPageContents,
                        SubPages = RecursiveSubPages(subPage.Id, cancellationToken),
                        Status = (BaseStatus)subPage.Status,
                        CreatedAt = (DateTime)subPage.DateCreated,
                        UpdatedAt = (DateTime)subPage.DateModified
                    });
                });
            }
            return data;
        }

        public async Task<List<PageJointGroupResponse>> GetPagesByTypeAsync(string type, Guid excludePageId, CancellationToken cancellationToken)
        {
            return await _context.Pages
                .Where(x => x.Type == type && x.Id != excludePageId && !x.Deleted)
                .Select(x => new PageJointGroupResponse
                {
                    Title = x.Title,
                    Icon = x.Icon,
                    Slug = x.Slug
                })
                .ToListAsync(cancellationToken);
        }
    }
}

