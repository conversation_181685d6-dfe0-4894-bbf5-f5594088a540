using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using TmobCore.Cms.Domain.Entities;

namespace TmobCore.Cms.Infrastructure.Database.Configurations
{
    public class LanguageConfiguration : IEntityTypeConfiguration<Language>
    {
        public void Configure(EntityTypeBuilder<Language> builder)
        {
            builder.HasKey(f => f.Id);
            builder.Property(b => b.Name).IsRequired();
            builder.Property(b => b.LanguageCode).IsRequired();
            builder.Property(b => b.Deleted).HasDefaultValue(false);
            
        }
    }
}
