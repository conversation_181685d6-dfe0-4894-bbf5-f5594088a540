using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using TmobCore.Cms.Domain.Common;
using TmobCore.Cms.Domain.Entities;

namespace TmobCore.Cms.Infrastructure.Database.Configurations
{
    public class BlogConfiguration : IEntityTypeConfiguration<Blog>
    {
        public void Configure(EntityTypeBuilder<Blog> builder)
        {
            builder.HasKey(b => b.Id);
            builder.Property(b => b.Title).IsRequired();
            builder.Property(b => b.BeginDate).IsRequired();
            builder.Property(b => b.EndDate).IsRequired();
            builder.Property(b => b.Status).HasDefaultValue(BaseStatus.Draft);
            builder.Property(b => b.Deleted).HasDefaultValue(false);
            builder.Property(b=> b.Slug).IsRequired();
            
            builder.Property(x => x.CardTitle).HasMaxLength(150);
            builder.Property(x => x.CardDescription).HasMaxLength(400);
            builder.Property(x => x.AirportCode).HasMaxLength(8);
            builder.Property(x => x.Visible).IsRequired().HasDefaultValue(false);
            builder.Property(x => x.IsProfileCampaign).IsRequired().HasDefaultValue(false);
            
            builder.HasOne(b => b.Image)
                .WithMany()
                .HasForeignKey(b => b.ImageId)
                .OnDelete(DeleteBehavior.SetNull);
                
            builder.HasMany(blog => blog.BlogContents)
                   .WithOne(content => content.Blog)
                   .HasForeignKey(content => content.BlogId);
        }
    }
}
