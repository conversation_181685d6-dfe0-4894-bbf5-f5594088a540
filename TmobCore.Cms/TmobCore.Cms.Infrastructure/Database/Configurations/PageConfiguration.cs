using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using TmobCore.Cms.Domain.Entities;
using TmobCore.Cms.Domain.Common;

namespace TmobCore.Cms.Infrastructure.Database.Configurations
{
    public class PageConfiguration : IEntityTypeConfiguration<Page>
    {
        public void Configure(EntityTypeBuilder<Page> builder)
        {
            builder.HasKey(p => p.Id);
            builder.Property(p => p.Title).IsRequired();
            builder.Property(p => p.Slug).IsRequired();
            builder.Property(p => p.Type).HasDefaultValue(string.Empty);
            builder.Property(p => p.Icon).HasDefaultValue(string.Empty);
            builder.Property(p => p.Order).HasDefaultValue(0);
            builder.Property(p => p.Status).HasDefaultValue(BaseStatus.Draft);
            builder.Property(p => p.Deleted).HasDefaultValue(false);

            builder.HasMany(p => p.SubPages)
                .WithOne(p => p.ParentPage)
                .HasForeignKey(p => p.ParentPageId);

            builder.HasMany(p => p.PageContents)
                .WithOne(pc => pc.Page)
                .HasForeignKey(pc => pc.PageId);

            builder.HasOne(n => n.Image)
                .WithMany()
                .HasForeignKey(n => n.ImageId)
                .OnDelete(DeleteBehavior.SetNull);
        }
    }
}