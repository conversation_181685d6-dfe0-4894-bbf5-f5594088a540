using TmobCore.Cms.Application.Models.Page;
using TmobCore.Cms.Domain.Entities;

namespace TmobCore.Cms.Application.Contracts.Persistence
{
    public interface IPageRepository : IGenericRepository<Page>
    {
        Task<bool> IsSlugUnique(string slug, Guid? id = null);
        List<PageResponse> RecursiveSubPages(Guid? parentId, CancellationToken cancellationToken);
        Task<List<PageJointGroupResponse>> GetPagesByTypeAsync(string type, Guid excludePageId, CancellationToken cancellationToken);
    }
}

