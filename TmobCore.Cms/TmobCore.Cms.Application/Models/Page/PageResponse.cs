using TmobCore.Cms.Application.Models.Common;
using TmobCore.Cms.Application.Models.Image;

namespace TmobCore.Cms.Application.Models.Page
{
    public class PageResponse : BaseResponse
    {
        public Guid Id { get; set; }
        public Guid GroupId { get; set; }
        public Guid? LanguageId { get; set; }
        public string Title { get; set; }
        public string Slug { get; set; }
        public string Type { get; set; } = string.Empty;
        public string Icon { get; set; } = string.Empty;
        public int Order { get; set; }
        public DateTime PublishDate { get; set; }
        public List<PageResponse> SubPages { get; set; } = new();
        public List<PageContentResponse> PageContents { get; set; }
        public ImageResponse File { get; set; }
        public Dictionary<Guid, string> LocalizedSlugs { get; set; } = new();
        public List<PageJointGroupResponse> JointGroup { get; set; } = new();
    }
}