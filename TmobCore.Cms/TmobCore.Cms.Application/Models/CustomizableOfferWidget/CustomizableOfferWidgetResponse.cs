using TmobCore.Cms.Application.Models.Common;
using TmobCore.Cms.Domain.Enums;

namespace TmobCore.Cms.Application.Models.CustomizableOfferWidget
{
    public class CustomizableOfferWidgetResponse
    {
        public Guid Id { get; set; }
        public string Title { get; set; } = null!;
        public bool Visible { get; set; }
        public int Priority { get; set; }
        public Tag Tag { get; set; }

        public DateTime FlightStartDate { get; set; }
        public DateTime FlightEndDate { get; set; }
        public DateTime PublishedStartDate { get; set; }
        public DateTime PublishedEndDate { get; set; }

        public List<CustomizableOfferRouteResponse> Routes { get; set; } = new();
    }

    public class CustomizableOfferRouteResponse
    {
        public string Origin { get; set; } = null!;
        public string Destination { get; set; } = null!;
        public List<PriceInCurrencyResponse> Prices { get; set; } = new();
    }
}