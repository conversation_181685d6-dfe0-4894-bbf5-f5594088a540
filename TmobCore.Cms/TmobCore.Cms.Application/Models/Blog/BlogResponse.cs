using TmobCore.Cms.Application.Models.Common;
using TmobCore.Cms.Application.Models.Image;

namespace TmobCore.Cms.Application.Models.Blog
{
    public class BlogResponse : BaseResponse
    {
        public Guid Id { get; set; }
        public Guid UserId { get; set; }
        public string Title { get; set; }
        public string Slug { get; set; }
        public Guid GroupId { get; set; }
        public Guid LanguageId { get; set; }
        public DateTime BeginDate { get; set; }
        public DateTime EndDate { get; set; }
        public string Tags { get; set; } = string.Empty;
        public int ReadingTime { get; set; } = 0;
        
        public string? CardTitle { get; set; }
        public string? CardDescription { get; set; }
        public string? AirportCode { get; set; }
        public bool Visible { get; set; }
        public bool IsProfileCampaign { get; set; }
        
        public List<BlogFilterResponse> Filters { get; set; } = new();
        public List<BlogContentResponse> BlogContents { get; set; } = new();
        public ImageResponse File { get; set; }
        public Dictionary<Guid, string> LocalizedSlugs { get; set; } = new();
        
    }
}
