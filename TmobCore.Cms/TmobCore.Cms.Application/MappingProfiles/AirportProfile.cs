using AutoMapper;
using TmobCore.Cms.Application.Features.Airport.Commands.CreateAirport;
using TmobCore.Cms.Application.Features.Airport.Commands.UpdateAirport;
using TmobCore.Cms.Application.Models.Airport;
using TmobCore.Cms.Application.Models.City;
using TmobCore.Cms.Application.Models.Country;
using TmobCore.Cms.Domain.Entities;

namespace TmobCore.Cms.Application.MappingProfiles
{
    public class AirportProfile : Profile
    {
        public AirportProfile()
        {
            CreateMap<CreateAirportRequest, Airport>()
                .ForMember(dest => dest.Id, opt => opt.Ignore())
                .ForMember(dest => dest.DateCreated, opt => opt.MapFrom(src => DateTime.UtcNow))
                .ForMember(dest => dest.DateModified, opt => opt.Ignore())
                .ForMember(dest => dest.LanguageId, opt => opt.Ignore())
                .ForMember(dest => dest.City, opt => opt.Ignore())
                .ForMember(dest => dest.IsDeleted, opt => opt.MapFrom(src => false));

            CreateMap<UpdateAirportRequest, Airport>()
                .ForMember(dest => dest.DateModified, opt => opt.MapFrom(src => DateTime.UtcNow));

            CreateMap<Airport, AirportResponse>()
                .ForMember(dest => dest.Order, opt => opt.MapFrom(src => src.Order))
                .ForMember(dest => dest.File, opt => opt.MapFrom(src => src.Image))
                .ForMember(dest => dest.City, opt => opt.MapFrom(src => src.City))
                .ForMember(dest => dest.Country, opt => opt.MapFrom(src => src.City.Country))
                .ForMember(dest => dest.StatusCode, opt => opt.Ignore()) // Will be set manually in handlers
                .ForMember(dest => dest.AlternativeAirports, opt => opt.Ignore()); // Will be set manually in handlers

            CreateMap<Airport, AlternativeAirportResponse>()
                .ForMember(dest => dest.City, opt => opt.MapFrom(src => src.City))
                .ForMember(dest => dest.Country, opt => opt.MapFrom(src => src.City.Country));

            CreateMap<City, CityResponse>();
            CreateMap<Country, CountryResponse>();
        }
    }

}
