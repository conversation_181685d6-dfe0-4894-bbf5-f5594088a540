using AutoMapper;
using TmobCore.Cms.Application.Features.Page.Commands.CreatePage;
using TmobCore.Cms.Application.Models.Page;
using TmobCore.Cms.Domain.Entities;

namespace TmobCore.Cms.Application.MappingProfiles
{
    public class PageProfile : Profile
    {
        public PageProfile()
        {
            CreateMap<CreatePageRequest, Page>().ReverseMap();
            CreateMap<PageContentRequest, PageContent>().ReverseMap();
            CreateMap<PageContent, PageContentResponse>().ReverseMap();
            CreateMap<Page, PageResponse>()
                .ForMember(dest => dest.CreatedAt, opt => opt.MapFrom(src => src.DateCreated))
                .ForMember(dest => dest.UpdatedAt, opt => opt.MapFrom(src => src.DateModified))
                .ForMember(dest => dest.File, opt => opt.MapFrom(src => src.Image))
                .ForMember(dest => dest.JointGroup, opt => opt.Ignore()) // Will be populated separately
                .ReverseMap();
        }
    }
}