using AutoMapper;
using TmobCore.Cms.Application.Features.ExploreOfferWidget.Commands.Create;
using TmobCore.Cms.Application.Features.ExploreOfferWidget.Commands.Update;
using TmobCore.Cms.Application.Models.Common;
using TmobCore.Cms.Application.Models.ExploreOfferWidget;
using TmobCore.Cms.Domain.Entities;

namespace TmobCore.Cms.Application.MappingProfiles
{
    public class ExploreOfferWidgetProfile : Profile
    {
        public ExploreOfferWidgetProfile()
        {
            CreateMap<CreateExploreOfferWidgetRequest, CreateExploreOfferWidgetCommand>();
            CreateMap<CreateExploreOfferWidgetCommand, ExploreOfferWidget>()
                .ForMember(dest => dest.Routes, opt => opt.Ignore());
            
            CreateMap<ExploreOfferWidget, ExploreOfferWidgetResponse>();
            CreateMap<ExploreOfferRoute, ExploreOfferRouteResponse>()
                .ForMember(dest => dest.Destination, opt => opt.MapFrom(src => src.AirportCode));
            CreateMap<ExploreOfferPrice, PriceInCurrencyResponse>()
                .ForMember(dest => dest.Currency, opt => opt.MapFrom(src => src.Currency.ToString()));

            CreateMap<UpdateExploreOfferWidgetRequest, UpdateExploreOfferWidgetCommand>();

        }
    }
}