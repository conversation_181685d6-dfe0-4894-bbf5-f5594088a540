using MediatR;
using TmobCore.Cms.Application.Models.Page;
using TmobCore.Cms.Application.Models.Common;

namespace TmobCore.Cms.Application.Features.Page.Commands.UpdatePage
{
    public class UpdatePageCommand : IRequest<ActionResponse<bool>>
    {
        public List<UpdatePageRequest> Pages { get; set; } = new();
    }

    public class UpdatePageRequest : BaseRequest
    {
        public Guid Id { get; set; }
        public string Title { get; set; }
        public string Slug { get; set; }
        public string Type { get; set; } = string.Empty;
        public string Icon { get; set; } = string.Empty;
        public Guid? ParentPageId { get; set; }
        public int Order { get; set; }
        public Guid? ImageId { get; set; }
        public BaseStatus Status { get; set; }
        public DateTime PublishDate { get; set; }
        public List<PageContentUpdateRequest>? Contents { get; set; } = new List<PageContentUpdateRequest>();
    }
}