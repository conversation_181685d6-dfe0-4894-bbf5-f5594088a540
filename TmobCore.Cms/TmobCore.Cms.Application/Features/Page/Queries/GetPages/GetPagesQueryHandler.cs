using AutoMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using TmobCore.Cms.Application.Contracts.Logging;
using TmobCore.Cms.Application.Contracts.Persistence;
using TmobCore.Cms.Application.Contracts.User;
using TmobCore.Cms.Application.Helper;
using TmobCore.Cms.Application.Models.Common;
using TmobCore.Cms.Application.Models.Image;
using TmobCore.Cms.Application.Models.Page;

namespace TmobCore.Cms.Application.Features.Page.Queries.GetPages
{
    public class GetPagesQueryHandler : IRequestHandler<GetPagesQuery, ActionResponse<List<PageResponse>>>
    {
        private readonly IPageRepository _pageRepository;
        private readonly IPageContentRepository _pageContentRepository;
        private readonly IUserPrincipal _userPrincipal;
        private readonly IAppLogger<GetPagesQueryHandler> _logger;
        private readonly IMapper _mapper;

        public GetPagesQueryHandler(IPageRepository pageRepository, 
                                    IPageContentRepository pageContentRepository,
                                    IUserPrincipal userPrincipal,
                                    IAppLogger<GetPagesQueryHandler> logger,
                                    IMapper mapper)
        {
            _pageRepository = pageRepository;
            _pageContentRepository = pageContentRepository;
            _userPrincipal = userPrincipal;
            _mapper = mapper;
            _logger = logger;
        }

        public async Task<ActionResponse<List<PageResponse>>> Handle(GetPagesQuery request, CancellationToken cancellationToken)
        {
            try
            {
                var projectId = _userPrincipal.ProjectId.ToGuid();
                var pages = await _pageRepository.GetQuery(
                    x => (projectId == null || x.ProjectId == projectId) &&
                         !x.Deleted &&
                         x.ParentPageId == null &&
                         (request.GroupId == null || x.GroupId == request.GroupId) &&
                         (request.LanguageId == null || x.LanguageId == request.LanguageId) &&
                         (string.IsNullOrEmpty(request.Type) || x.Type == request.Type))
                    .Include(x => x.Image)
                    .ToListAsync(cancellationToken);

                var pageResponses = new List<PageResponse>();

                foreach (var page in pages)
                {
                    var pageContents = await _pageContentRepository.GetQuery(
                        x => x.PageId == page.Id && !x.Deleted)
                        .Select(y => new PageContentResponse 
                        { 
                            Id = y.Id, 
                            Content = y.Content, 
                           Order = y.Order
                        })
                        .ToListAsync(cancellationToken);

                    pageResponses.Add(new PageResponse
                    {
                        Id = page.Id,
                        Title = page.Title,
                        Order = page.Order,
                        GroupId = page.GroupId,
                        LanguageId = page.LanguageId,
                        Slug = page.Slug,
                        Type = page.Type,
                        Icon = page.Icon,
                        File = _mapper.Map<ImageResponse>(page.Image),
                        PublishDate = page.PublishDate,
                        PageContents = pageContents,
                        SubPages = _pageRepository.RecursiveSubPages(page.Id, cancellationToken),
                        Status = (BaseStatus)page.Status,
                        CreatedAt = (DateTime)page.DateCreated,
                        UpdatedAt = (DateTime)page.DateModified
                    });
                }

                return ActionResponse<List<PageResponse>>.Success(pageResponses, StatusCode.Ok);
            }
            catch (Exception ex)
            {
                _logger.LogWarning("Failed to get pages. Error: {Error}", ex.Message);
                return ActionResponse<List<PageResponse>>.Fail(ex.Message, StatusCode.BadRequest);
            }
        }
    }
}
