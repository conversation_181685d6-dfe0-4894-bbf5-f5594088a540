using AutoMapper;
using MediatR;
using TmobCore.Cms.Application.Contracts.Logging;
using TmobCore.Cms.Application.Contracts.Persistence;
using TmobCore.Cms.Application.Models.Common;
using TmobCore.Cms.Application.Models.Page;

namespace TmobCore.Cms.Application.Features.Page.Queries.FindPageBySlug;

public class FindPageBySlugQueryHandler: IRequestHandler<FindPageBySlugQuery,ActionResponse<PageResponse>>
{

    private readonly IPageRepository _pageRepository;
    private readonly IAppLogger<FindPageBySlugQueryHandler> _logger;
    private readonly IMapper _mapper;
    
    public FindPageBySlugQueryHandler(IPageRepository pageRepository, IAppLogger<FindPageBySlugQueryHandler> logger, IMapper mapper)
    {
        _pageRepository = pageRepository;
        _logger = logger;
        _mapper = mapper;
    }
    public async Task<ActionResponse<PageResponse>> Handle(FindPageBySlugQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var validator = new FindPageBySlugQueryValidator(_pageRepository);
            var validationResult = await validator.ValidateAsync(request, cancellationToken);
            if (!validationResult.IsValid)
            {
                _logger.LogWarning($"Validation failed for FindPageBySlug. Errors: {string.Join(", ", validationResult.Errors)}");
                return ActionResponse<PageResponse>.Fail(validationResult.Errors.First().ErrorMessage, StatusCode.BadRequest);
            }
            var page = await _pageRepository.GetBySlugWithIncludesAsync(
                request.Slug,
                cancellationToken,
                x => ((Domain.Entities.Page)(object)x).PageContents
            );
            var data = _mapper.Map<PageResponse>(page);

            var localizedSlugs = await _pageRepository.GetLocalizedSlugsBySlugAsync(request.Slug);
            data.LocalizedSlugs = localizedSlugs;

            // Get joint group - pages with the same type
            if (!string.IsNullOrEmpty(page.Type))
            {
                data.JointGroup = await _pageRepository.GetPagesByTypeAsync(page.Type, page.Id, cancellationToken);
            }

            return ActionResponse<PageResponse>.Success(data, StatusCode.Ok);
        }
        catch (Exception ex)
        {
            _logger.LogWarning("FindPageBySlug error", request.Slug, ex);
            return ActionResponse<PageResponse>.Fail($"An error was accoured. Error Message is {ex}", StatusCode.BadRequest);
        }
    }
}