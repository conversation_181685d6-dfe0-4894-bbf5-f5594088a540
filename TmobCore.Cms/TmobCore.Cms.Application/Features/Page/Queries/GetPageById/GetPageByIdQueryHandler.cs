using AutoMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using TmobCore.Cms.Application.Contracts.Logging;
using TmobCore.Cms.Application.Contracts.Persistence;
using TmobCore.Cms.Application.Models.Common;
using TmobCore.Cms.Application.Models.Image;
using TmobCore.Cms.Application.Models.Page;

namespace TmobCore.Cms.Application.Features.Page.Queries.GetPageById
{
    public class GetPageByIdQueryHandler : IRequestHandler<GetPageByIdQuery, ActionResponse<PageResponse>>
    {
        private readonly IPageRepository _pageRepository;
        private readonly IPageContentRepository _pageContentRepository;
        private readonly IAppLogger<GetPageByIdQueryHandler> _logger;
        private readonly IMapper _mapper;
        public GetPageByIdQueryHandler(IPageRepository pageRepository, IPageContentRepository pageContentRepository, IAppLogger<GetPageByIdQueryHandler> logger, IMapper mapper)
        {
            _pageRepository = pageRepository;
            _pageContentRepository = pageContentRepository;
            _mapper = mapper;
            _logger = logger;
        }
        public async Task<ActionResponse<PageResponse>> Handle(GetPageByIdQuery request, CancellationToken cancellationToken)
        {
            try
            {
                var page = await _pageRepository.GetQuery(x=>x.Id == request.Id && !x.Deleted, z=> z.PageContents, ic => ic.Image).FirstOrDefaultAsync(cancellationToken);
                if (page != null)
                {
                    var pageContents = await _pageContentRepository.GetQuery(x => x.PageId == page.Id && !x.Deleted)?
                        .Select(y => new PageContentResponse { Id = y.Id, Content = y.Content, Order = y.Order})?
                        .ToListAsync(cancellationToken);
                    var pageResponse= new PageResponse {
                        Id = page.Id,
                        Title  = page.Title,
                        Slug = page.Slug,
                        Type = page.Type,
                        Icon = page.Icon,
                        Order = page.Order,
                        File = _mapper.Map<ImageResponse>(page.Image),
                        PublishDate = page.PublishDate,
                        PageContents = pageContents,
                        SubPages = _pageRepository.RecursiveSubPages(page.Id, cancellationToken),
                        Status = (BaseStatus)page.Status,
                        CreatedAt = (DateTime)page.DateCreated,
                        UpdatedAt = (DateTime)page.DateModified,
                        LanguageId = page.LanguageId,
                        GroupId = page.GroupId
                    };
                    
                    return ActionResponse<PageResponse>.Success(pageResponse, StatusCode.Ok);
                }
                _logger.LogWarning($"Page not found or deleted for request id {request.Id}");
                return ActionResponse<PageResponse>.Fail($"Page not found for this id {request.Id}", StatusCode.NotFound);
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"Page not found", request, ex);
                return ActionResponse<PageResponse>.Fail($"Error accoured. Error message is {ex.ToString()}", StatusCode.BadRequest);
            }
        }
    }
}