using MediatR;
using TmobCore.Cms.Application.Contracts.Logging;
using TmobCore.Cms.Application.Contracts.Persistence;
using TmobCore.Cms.Application.Models.Common;

namespace TmobCore.Cms.Application.Features.News.Commands.DeleteNews
{
    public class DeleteNewsItemsByIdCommandHandler : IRequestHandler<DeleteNewsItemsByIdCommand, ActionResponse<int>>
    {
        private readonly INewsRepository _newsRepository;
        private readonly IAppLogger<DeleteNewsItemsByIdCommandHandler> _logger;
        private readonly IUnitOfWork _uow;

        public DeleteNewsItemsByIdCommandHandler(INewsRepository newsRepository,
                                               IAppLogger<DeleteNewsItemsByIdCommandHandler> logger,
                                               IUnitOfWork uow)
        {
            _newsRepository = newsRepository;
            _logger = logger;
            _uow = uow;
        }

        public async Task<ActionResponse<int>> Handle(DeleteNewsItemsByIdCommand request, CancellationToken cancellationToken)
        {
            try
            {
                // Validate the delete request
                var validator = new DeleteNewsItemsByIdCommandValidator(_newsRepository);
                var validationResult = await validator.ValidateAsync(request, cancellationToken);

                if (!validationResult.IsValid)
                {
                    var validationErrors = string.Join(", ", validationResult.Errors.Select(x => x.ErrorMessage));
                    _logger.LogWarning($"News deletion failed due to validation errors: {validationErrors}");
                    return ActionResponse<int>.Fail(validationErrors, StatusCode.BadRequest);
                }

                var deletedCount = 0;
                var invalidIds = new List<Guid>();

                foreach (var id in request.Ids)
                {
                    var news = await _newsRepository.GetByIdAsync(id);
                    if (news != null && !news.Deleted)
                    {
                        news.Deleted = true;
                        _newsRepository.Update(news);
                        deletedCount++;
                    }
                    else
                    {
                        invalidIds.Add(id);
                    }
                }

                if (invalidIds.Any())
                {
                    _logger.LogWarning($"Some news IDs were invalid: {string.Join(", ", invalidIds)}");
                    return ActionResponse<int>.Fail($"Invalid news IDs: {string.Join(", ", invalidIds)}", StatusCode.BadRequest);
                }

                if (deletedCount == 0)
                {
                    _logger.LogWarning("No news found to delete", request.Ids);
                    return ActionResponse<int>.Fail("No news found to delete", StatusCode.NotFound);
                }

                await _uow.SaveChangesAsync(cancellationToken);
                _logger.LogInformation($"{deletedCount} news have been deleted successfully", request.Ids);

                return ActionResponse<int>.Success(deletedCount, StatusCode.Ok);
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"An error occurred while deleting news - {ex.Message}", request.Ids);
                return ActionResponse<int>.Fail($"An error occurred while deleting news. Error: {ex.Message}", StatusCode.BadRequest);
            }
        }
    }
}
