using FluentValidation;
using TmobCore.Cms.Application.Contracts.Persistence;

namespace TmobCore.Cms.Application.Features.News.Commands.DeleteNews
{
    public class DeleteNewsItemsByIdCommandValidator : AbstractValidator<DeleteNewsItemsByIdCommand>
    {
        private readonly INewsRepository _newsRepository;

        public DeleteNewsItemsByIdCommandValidator(INewsRepository newsRepository)
        {
            _newsRepository = newsRepository;

            RuleFor(p => p.Ids)
                .NotEmpty().WithMessage("News IDs cannot be empty")
                .Must(x => x != null && x.Any()).WithMessage("At least one News ID must be provided");

            RuleForEach(p => p.Ids)
                .NotEmpty().WithMessage("News ID cannot be empty")
                .MustAsync(async (id, cancellation) => await NewsExists(id))
                .WithMessage("News with ID {PropertyValue} does not exist");
        }

        private async Task<bool> NewsExists(Guid id)
        {
            var news = await _newsRepository.GetByIdAsync(id);
            return news != null && !news.Deleted;
        }
    }
}
