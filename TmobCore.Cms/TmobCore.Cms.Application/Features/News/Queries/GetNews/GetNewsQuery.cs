using MediatR;
using TmobCore.Cms.Application.Models.Common;
using TmobCore.Cms.Application.Models.News;

namespace TmobCore.Cms.Application.Features.News.Queries.GetNews;

public class GetNewsQuery: IRequest<ActionResponse<List<NewsResponse>>>
{
    public string? SearchTerm { get; set; }
    public int Page { get; set; } = 1;
    public int PageSize { get; set; } = 10000;
    public Guid? GroupId { get; set; }
}