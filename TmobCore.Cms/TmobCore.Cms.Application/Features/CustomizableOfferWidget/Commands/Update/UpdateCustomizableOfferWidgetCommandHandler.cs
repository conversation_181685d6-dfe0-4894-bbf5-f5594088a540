using AutoMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using TmobCore.Cms.Application.Contracts.Logging;
using TmobCore.Cms.Application.Contracts.Persistence;
using TmobCore.Cms.Application.Models.Common;
using TmobCore.Cms.Domain.Common;
using TmobCore.Cms.Domain.Entities;

namespace TmobCore.Cms.Application.Features.CustomizableOfferWidget.Commands.Update
{
    public class UpdateCustomizableOfferWidgetCommandHandler : IRequestHandler<UpdateCustomizableOfferWidgetCommand, ActionResponse<Guid>>
    {
        private readonly ICustomizableOfferWidgetRepository _repository;
        private readonly IUnitOfWork _uow;
        private readonly IAppLogger<UpdateCustomizableOfferWidgetCommandHandler> _logger;
        private readonly IMapper _mapper;

        public UpdateCustomizableOfferWidgetCommandHandler(
            ICustomizableOfferWidgetRepository repository,
            IUnitOfWork uow,
            IAppLogger<UpdateCustomizableOfferWidgetCommandHandler> logger,
            IMapper mapper)
        {
            _repository = repository;
            _uow = uow;
            _logger = logger;
            _mapper = mapper;
        }

        public async Task<ActionResponse<Guid>> Handle(UpdateCustomizableOfferWidgetCommand request, CancellationToken cancellationToken)
        {
            var widget = await _repository.GetQuery(x => x.Id == request.Id && !x.Deleted)
                .Include(x => x.Routes)
                    .ThenInclude(r => r.Prices)
                .FirstOrDefaultAsync(cancellationToken);

            if (widget == null)
                return ActionResponse<Guid>.Fail("Widget not found", StatusCode.NotFound);

            widget.Title = request.Title;
            widget.Visible = request.Visible;
            widget.Priority = request.Priority;
            widget.Tag = request.Tag;
            widget.FlightStartDate = request.FlightStartDate;
            widget.FlightEndDate = request.FlightEndDate;
            widget.PublishedStartDate = request.PublishedStartDate;
            widget.PublishedEndDate = request.PublishedEndDate;

            // Eski alt kayıtları temizle
            widget.Routes.Clear();

            foreach (var route in request.Routes)
            {
                var routeEntity = new CustomizableOfferRoute
                {
                    Origin = route.Origin,
                    Destination = route.Destination,
                    Prices = route.Prices.Select(p => new CustomizableOfferPrice
                    {
                        MaxPrice = p.MaxPrice,
                        Currency = Enum.Parse<CurrencyType>(p.Currency, true)
                    }).ToList()
                };

                widget.Routes.Add(routeEntity);
            }

            _repository.Update(widget);
            await _uow.SaveChangesAsync(cancellationToken);

            return ActionResponse<Guid>.Success(widget.Id, StatusCode.Ok);
        }
    }
}
