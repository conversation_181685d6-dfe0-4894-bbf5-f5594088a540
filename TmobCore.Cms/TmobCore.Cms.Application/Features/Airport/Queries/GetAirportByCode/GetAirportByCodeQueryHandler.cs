using AutoMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using TmobCore.Cms.Application.Contracts.Logging;
using TmobCore.Cms.Application.Contracts.Persistence;
using TmobCore.Cms.Application.Models.Airport;
using TmobCore.Cms.Application.Models.Common;

namespace TmobCore.Cms.Application.Features.Airport.Queries.GetAirportByCode
{
    public class GetAirportByCodeQueryHandler : IRequestHandler<GetAirportByCodeQuery, ActionResponse<AirportResponse>>
    {
        private readonly IAirportRepository _airportRepository;
        private readonly IMapper _mapper;
        private readonly IAppLogger<GetAirportByCodeQueryHandler> _logger;
        public GetAirportByCodeQueryHandler(IAirportRepository airportRepository, IMapper mapper, IAppLogger<GetAirportByCodeQueryHandler> logger)
        {
            _airportRepository = airportRepository;
            _mapper = mapper;
            _logger = logger;
        }
        public async Task<ActionResponse<AirportResponse>> Handle(GetAirportByCodeQuery request, CancellationToken cancellationToken)
        {
            try
            {
                var airport = await _airportRepository.GetQuery(x => x.Code == request.Code && x.IsActive && x.IsDeleted == false).Include(a => a.Image).FirstOrDefaultAsync(cancellationToken);
                if (airport == null)
                {
                    _logger.LogWarning($"Airport with Code {request.Code} not found.");
                    return ActionResponse<AirportResponse>.Fail($"Airport with Code {request.Code} not found.", StatusCode.NotFound);
                }

                var response = _mapper.Map<AirportResponse>(airport);

                // Set StatusCode
                response.StatusCode = (int)StatusCode.Ok;

                // Populate AlternativeAirports
                var alternativeAirports = await _airportRepository.GetAlternativeAirportObjectsByCode(airport.Code);
                response.AlternativeAirports = _mapper.Map<List<AlternativeAirportResponse>>(alternativeAirports) ?? new List<AlternativeAirportResponse>();

                return ActionResponse<AirportResponse>.Success(response, StatusCode.Ok);
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"Error getting airport by code {request.Code}", request, ex.Message);
                return ActionResponse<AirportResponse>.Fail($"Error getting airport by code {request.Code}", StatusCode.InternalServerError);
            }
        }
    }
}
