using AutoMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using TmobCore.Cms.Application.Contracts.Logging;
using TmobCore.Cms.Application.Contracts.Persistence;
using TmobCore.Cms.Application.Models.Airport;
using TmobCore.Cms.Application.Models.Common;

namespace TmobCore.Cms.Application.Features.Airport.Queries.GetAirportById
{
    public class GetAirportByIdQueryHandler : IRequestHandler<GetAirportByIdQuery, ActionResponse<AirportResponse>>
    {
        private readonly IAirportRepository _airportRepository;
        private readonly IMapper _mapper;
        private readonly IAppLogger<GetAirportByIdQueryHandler> _logger;
        public GetAirportByIdQueryHandler(IAirportRepository airportRepository, IMapper mapper, IAppLogger<GetAirportByIdQueryHandler> logger)
        {
            _airportRepository = airportRepository;
            _mapper = mapper;
            _logger = logger;
        }
        public async Task<ActionResponse<AirportResponse>> Handle(GetAirportByIdQuery request, CancellationToken cancellationToken)
        {
            try
            {
                var airport = await _airportRepository.GetQuery(x => x.Id == request.Id && x.IsActive && x.IsDeleted == false)
                    .Include(a => a.Image)
                    .Include(a => a.City)
                    .ThenInclude(c => c.Country)
                    .FirstOrDefaultAsync(cancellationToken);

                if (airport == null)
                {
                    _logger.LogWarning($"Airport with Id {request.Id} not found.");
                    return ActionResponse<AirportResponse>.Fail($"Airport with Id {request.Id} not found.", StatusCode.NotFound);
                }

                var response = _mapper.Map<AirportResponse>(airport);

                // Set StatusCode
                response.StatusCode = (int)StatusCode.Ok;

                // Populate AlternativeAirports
                var alternativeAirports = await _airportRepository.GetAlternativeAirportObjectsByCode(airport.Code);
                response.AlternativeAirports = _mapper.Map<List<AlternativeAirportResponse>>(alternativeAirports) ?? new List<AlternativeAirportResponse>();

                return ActionResponse<AirportResponse>.Success(response, StatusCode.Ok);
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"Error getting airport by Id {request.Id}", request, ex.Message);
                return ActionResponse<AirportResponse>.Fail($"Error getting airport by Id {request.Id}", StatusCode.InternalServerError);
            }
        }
    }
}
