using MediatR;
using TmobCore.Cms.Application.Models.Airport;
using TmobCore.Cms.Application.Models.Common;

namespace TmobCore.Cms.Application.Features.Airport.Commands.CreateAirport
{
    public class CreateAirportCommand : IRequest<ActionResponse<List<AirportResponse>>>
    {
        public List<CreateAirportRequest> Airports { get; set; } = new();
    }

    public class CreateAirportRequest : BaseRequest
    {
        public string Code { get; set; }
        public string Name { get; set; }
        public double Latitude { get; set; }
        public double Longitude { get; set; }
        public string AlternativeAirports { get; set; }
        public int Order { get; set; }
        public Guid CityId { get; set; }
        public Guid? ImageId { get; set; }
        public bool IsActive { get; set; } = true;
        public string Slug { get; set; } = String.Empty;
    }
}
