using AutoMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using TmobCore.Cms.Application.Contracts.Logging;
using TmobCore.Cms.Application.Contracts.Persistence;
using TmobCore.Cms.Application.Contracts.User;
using TmobCore.Cms.Application.Exceptions;
using TmobCore.Cms.Application.Helper;
using TmobCore.Cms.Application.Models.Airport;
using TmobCore.Cms.Application.Models.Common;

namespace TmobCore.Cms.Application.Features.Airport.Commands.CreateAirport
{
    public class CreateAirportCommandHandler : IRequestHandler<CreateAirportCommand, ActionResponse<List<AirportResponse>>>
    {
        private readonly IAirportRepository _airportRepository;
        private readonly ICityRepository _cityRepository;
        private readonly IUserPrincipal _userPrincipal;
        private readonly IAppLogger<CreateAirportCommandHandler> _logger;
        private readonly IMapper _mapper;
        private readonly IUnitOfWork _uow;
        public CreateAirportCommandHandler(IAirportRepository airportRepository,
            ICityRepository cityRepository,
            IUserPrincipal userPrincipal,
            IMapper mapper,
            IUnitOfWork uow,
            IAppLogger<CreateAirportCommandHandler> logger)
        {
            _airportRepository = airportRepository;
            _cityRepository = cityRepository;
            _userPrincipal = userPrincipal;
            _logger = logger;
            _mapper = mapper;
            _uow = uow;
        }
        public async Task<ActionResponse<List<AirportResponse>>> Handle(CreateAirportCommand request, CancellationToken cancellationToken)
        {
            try
            {
                var projectId = _userPrincipal.ProjectId.ToGuid()
                                ?? throw new NotFoundException("ProjectId", _userPrincipal.ProjectId);

                // Determine the groupId to use for all airports
                // If any airport has a non-empty groupId, use it; otherwise generate a new one
                Guid groupId = DetermineGroupId(request.Airports);
                List<Domain.Entities.Airport> airports = new();

                foreach (var airportRequest in request.Airports)
                {
                    var airport = _mapper.Map<Domain.Entities.Airport>(airportRequest);
                    airport.ImageId = FileExtentions.HandleFileIdAssignment(airportRequest.ImageId, null);
                    airport.UserId = _userPrincipal.UserId;
                    airport.ProjectId = projectId;
                    // Use the determined groupId for all language variants
                    airport.GroupId = groupId;
                    airport.LanguageId = airportRequest.LanguageId;
                    airports.Add(airport);
                }

                await _airportRepository.CreateRangeAsync(airports);
                await _uow.SaveChangesAsync(cancellationToken);

                // Fetch the created airports with their related data to return in response
                var createdAirports = await _airportRepository.GetQuery(a => airports.Select(ap => ap.Id).Contains(a.Id))
                    .Include(a => a.Image)
                    .Include(a => a.City)
                    .ThenInclude(c => c.Country)
                    .ToListAsync(cancellationToken);

                var airportResponses = _mapper.Map<List<AirportResponse>>(createdAirports);

                // Populate StatusCode and AlternativeAirports for each created airport
                foreach (var airportResponse in airportResponses)
                {
                    airportResponse.StatusCode = (int)StatusCode.Created;

                    // Find the corresponding airport entity to get the code
                    var airportEntity = createdAirports.FirstOrDefault(a => a.Id == airportResponse.Id);
                    if (airportEntity != null)
                    {
                        var alternativeAirports = await _airportRepository.GetAlternativeAirportObjectsByCode(airportEntity.Code);
                        airportResponse.AlternativeAirports = _mapper.Map<List<AlternativeAirportResponse>>(alternativeAirports) ?? new List<AlternativeAirportResponse>();
                    }
                    else
                    {
                        airportResponse.AlternativeAirports = new List<AlternativeAirportResponse>();
                    }
                }

                _logger.LogInformation($"Airport group created successfully with GroupId: {groupId}");
                return ActionResponse<List<AirportResponse>>.Success(airportResponses, StatusCode.Created);
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"Airport creation failed due to an error: {ex.Message}", request, ex.Message);
                return ActionResponse<List<AirportResponse>>.Fail(ex.Message, StatusCode.InternalServerError);
            }
        }

        /// <summary>
        /// Determines the groupId to use for all airport language variants.
        /// If any airport has a non-empty groupId, use it; otherwise generate a new one.
        /// This ensures all language variants share the same groupId.
        /// </summary>
        /// <param name="airports">List of airport requests</param>
        /// <returns>The groupId to use for all airports</returns>
        private Guid DetermineGroupId(List<CreateAirportRequest> airports)
        {
            // Check if any airport request has a provided groupId (not empty)
            var providedGroupId = airports
                .Where(a => a.GroupId.HasValue && a.GroupId.Value != Guid.Empty)
                .Select(a => a.GroupId.Value)
                .FirstOrDefault();

            // If a valid groupId is provided, use it; otherwise generate a new one
            return providedGroupId != Guid.Empty ? providedGroupId : Guid.NewGuid();
        }
    }
}
