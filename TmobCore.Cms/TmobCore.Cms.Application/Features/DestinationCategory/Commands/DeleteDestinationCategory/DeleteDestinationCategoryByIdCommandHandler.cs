using MediatR;
using TmobCore.Cms.Application.Contracts.Logging;
using TmobCore.Cms.Application.Contracts.Persistence;
using TmobCore.Cms.Application.Models.Common;

namespace TmobCore.Cms.Application.Features.DestinationCategory.Commands.DeleteDestinationCategory
{
    public class DeleteDestinationCategoryByIdCommandHandler : IRequestHandler<DeleteDestinationCategoryByIdCommand, ActionResponse<bool>>
    {
        private readonly IDestinationCategoryRepository _destinationCategoryRepository;
        private readonly IUnitOfWork _uow;
        private readonly IAppLogger<DeleteDestinationCategoryByIdCommandHandler> _logger;

        public DeleteDestinationCategoryByIdCommandHandler(IDestinationCategoryRepository destinationCategoryRepository,
            IUnitOfWork uow,
            IAppLogger<DeleteDestinationCategoryByIdCommandHandler> logger)
        {
            _destinationCategoryRepository = destinationCategoryRepository;
            _uow = uow;
            _logger = logger;
        }
        public async Task<ActionResponse<bool>> Handle(DeleteDestinationCategoryByIdCommand request, CancellationToken cancellationToken)
        {
            try
            {
                var destinationCategory = await _destinationCategoryRepository.GetByIdAsync(request.Id);
                if (destinationCategory == null)
                {
                    return ActionResponse<bool>.Fail("Destination Category not found.", StatusCode.NotFound);
                }

                destinationCategory.Deleted = true;
                _destinationCategoryRepository.Update(destinationCategory);

                await _uow.SaveChangesAsync(cancellationToken);
                return ActionResponse<bool>.Success(true, StatusCode.Ok);
            }
            catch (Exception ex)
            {
                _logger.LogWarning("DeleteDestinationCategoryByIdCommandHandler => Destination Category not deleted", request, ex);
                return ActionResponse<bool>.Fail(
                    $"Destination Category not deleted.Request: {request} Response: {ex}",
                    StatusCode.BadRequest);

            }
        }
    }
}
