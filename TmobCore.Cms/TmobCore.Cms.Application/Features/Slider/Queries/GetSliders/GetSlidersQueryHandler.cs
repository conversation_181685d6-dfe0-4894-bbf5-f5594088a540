using AutoMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using TmobCore.Cms.Application.Contracts.Logging;
using TmobCore.Cms.Application.Contracts.Persistence;
using TmobCore.Cms.Application.Contracts.User;
using TmobCore.Cms.Application.Helper;
using TmobCore.Cms.Application.Models.Common;
using TmobCore.Cms.Application.Models.Slider;

namespace TmobCore.Cms.Application.Features.Slider.Queries.GetSliders
{
    public class GetSlidersQueryHandler : IRequestHandler<GetSlidersQuery, ActionResponse<List<SliderResponse>>>
    {
        private readonly ISliderRepository _sliderRepository;
        private readonly IMapper _mapper;
        private readonly IUserPrincipal _userPrincipal;
        private readonly IAppLogger<GetSlidersQueryHandler> _logger;
        public GetSlidersQueryHandler(ISliderRepository sliderRepository, IMapper mapper, IUserPrincipal userPrincipal, IAppLogger<GetSlidersQueryHandler> logger)
        {
            _sliderRepository = sliderRepository;
            _userPrincipal = userPrincipal;
            _mapper = mapper;
            _logger = logger;
        }
        public async Task<ActionResponse<List<SliderResponse>>> Handle(GetSlidersQuery request, CancellationToken cancellationToken)
        {
            try
            {
                var projectId = _userPrincipal.ProjectId.ToGuid();

                // Build base query with all filters applied
                IQueryable<Domain.Entities.Slider> query;

                if (String.IsNullOrEmpty(request.SearchTerm))
                {
                    // Base query without search
                    query = _sliderRepository.GetQuery(
                        x => x.Deleted == false &&
                             (projectId == null || x.ProjectId == projectId) &&
                             (!request.GroupId.HasValue || x.GroupId == request.GroupId.Value),
                        i => i.Image);
                }
                else
                {
                    // Base query with search
                    query = _sliderRepository.GetQueryWithSearch(
                        x => x.Deleted == false && (projectId == null || x.ProjectId == projectId),
                        request.SearchTerm,
                        i => i.Image);
                }

                // Apply additional filters BEFORE pagination
                if (request.SliderType.HasValue)
                {
                    query = query.Where(x => x.SliderType == request.SliderType.Value);
                }

                if (request.LanguageId.HasValue)
                {
                    query = query.Where(x => x.LanguageId == request.LanguageId.Value);
                }

                // Calculate total count AFTER all filters are applied
                var totalCount = await query.CountAsync(cancellationToken);

                // Apply pagination AFTER all filters
                var sliders = await query
                    .Skip((request.Page - 1) * request.PageSize)
                    .Take(request.PageSize)
                    .ToListAsync(cancellationToken);

                var slidersResponse = _mapper.Map<List<SliderResponse>>(sliders);
                return ActionResponse<List<SliderResponse>>.Success(slidersResponse, StatusCode.Ok)
                    .WithPagination(request.Page, request.PageSize, totalCount);
            }
            catch (Exception ex)
            {
                _logger.LogWarning("GetSlidersQueryHandler => Sliders not returned", request, ex);
                return ActionResponse<List<SliderResponse>>.Fail($"Sliders not returned.Request: {_userPrincipal.ProjectId.ToGuid()} Response: {ex.ToString()}", StatusCode.BadRequest);
            }
        }
    }
}