using MediatR;
using TmobCore.Cms.Application.Models.Common;
using TmobCore.Cms.Application.Models.Slider;
using TmobCore.Cms.Domain.Enums;

namespace TmobCore.Cms.Application.Features.Slider.Queries.GetSliders
{
    public class GetSlidersQuery : IRequest<ActionResponse<List<SliderResponse>>>
    {
        public string? SearchTerm { get; set; }
        public int Page { get; set; } = 1;
        public int PageSize { get; set; } = 10000;
        public Guid? GroupId { get; set; }
        
        public SliderType? SliderType { get; set; }
        
        public Guid? LanguageId { get; set; }
        
        
    }
}