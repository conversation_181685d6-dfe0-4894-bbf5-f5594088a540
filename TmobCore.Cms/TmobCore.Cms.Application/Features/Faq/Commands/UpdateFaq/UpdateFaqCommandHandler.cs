using MediatR;
using TmobCore.Cms.Application.Contracts.Logging;
using TmobCore.Cms.Application.Contracts.Persistence;
using TmobCore.Cms.Application.Contracts.User;
using TmobCore.Cms.Application.Models.Common;

namespace TmobCore.Cms.Application.Features.Faq.Commands.UpdateFaq
{
    public class UpdateFaqCommandHandler : IRequestHandler<UpdateFaqCommand, ActionResponse<Guid>>
    {
        private readonly IFaqRepository _faqRepository;
        private readonly IAppLogger<UpdateFaqCommandHandler> _logger;
        private readonly IUnitOfWork _uow;
        private readonly IUserPrincipal _userPrincipal;

        public UpdateFaqCommandHandler(IFaqRepository faqRepository,
                                       IAppLogger<UpdateFaqCommandHandler> logger,
                                       IUnitOfWork uow,
                                       IUserPrincipal userPrincipal)
        {
            _faqRepository = faqRepository;
            _logger = logger;
            _uow = uow;
            _userPrincipal = userPrincipal;
        }
        public async Task<ActionResponse<Guid>> Handle(UpdateFaqCommand request, CancellationToken cancellationToken)
        {
            try
            {
                var faq = await _faqRepository.GetByIdAsync(request.Id);
                if (faq != null)
                {
                    var validator = new UpdateFaqCommandValidator(_faqRepository);
                    var validationResult = await validator.ValidateAsync(request);

                    if (!validationResult.IsValid)
                    {
                        _logger.LogWarning($"Faq update failed due to validation errors - {request}");
                        return ActionResponse<Guid>.Fail($"Faq update failed due to validation errors - {request}", StatusCode.BadRequest);
                    }

                    faq.Title = request.Title;
                    faq.Description = request.Description;
                    faq.Status = (Domain.Common.BaseStatus)request.Status;
                    faq.Tags = request.Tags;
                    faq.UserId = _userPrincipal.UserId;

                    _faqRepository.Update(faq);
                    await _uow.SaveChangesAsync(cancellationToken);
                    return ActionResponse<Guid>.Success(faq.Id, StatusCode.Ok);
                }

                return ActionResponse<Guid>.Fail("Faq not found", StatusCode.NotFound);

            }
            catch (Exception ex)
            {
                _logger.LogWarning($"Faq update failed due to exception errors Request: {request} - Response: {ex.ToString()}");
                return ActionResponse<Guid>.Fail(ex.ToString(), StatusCode.BadRequest);
            }
        }
    }
}
