using AutoMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using TmobCore.Cms.Application.Contracts.Persistence;
using TmobCore.Cms.Application.Contracts.User;
using TmobCore.Cms.Application.Features.HelpManagement.Queries.Get;
using TmobCore.Cms.Application.Helper;
using TmobCore.Cms.Application.Models.Common;
using TmobCore.Cms.Application.Models.HelpArticle;
using TmobCore.Cms.Application.Providers;

namespace TmobCore.Cms.Application.Features.HelpArticle.Queries.Get;

public class GetHelpArticlesQueryHandler : IRequestHandler<GetHelpArticlesQuery, ActionResponse<List<HelpArticleResponse>>>
{
    private readonly IHelpArticleRepository _repository;
    private readonly IMapper _mapper;
    private readonly IUserPrincipal _userPrincipal;

    public GetHelpArticlesQueryHandler(IHelpArticleRepository repository, IMapper mapper, IUserPrincipal userPrincipal)
    {
        _repository = repository;
        _mapper = mapper;
        _userPrincipal = userPrincipal;
    }

    public async Task<ActionResponse<List<HelpArticleResponse>>> Handle(GetHelpArticlesQuery request, CancellationToken cancellationToken)
    {
        var projectId = _userPrincipal.ProjectId.ToGuid();
        var query = _repository.GetQuery(x => !x.Deleted && x.ProjectId == projectId);

        if (request.GroupId.HasValue)
            query = query.Where(x => x.GroupId == request.GroupId.Value);

        if (request.LanguageId.HasValue)
            query = query.Where(x => x.LanguageId == request.LanguageId.Value);

        var articles = await query
            .Include(x => x.Category)
            .ToListAsync(cancellationToken);

        var response = _mapper.Map<List<HelpArticleResponse>>(articles);
        return ActionResponse<List<HelpArticleResponse>>.Success(response, StatusCode.Ok);
    }

}
