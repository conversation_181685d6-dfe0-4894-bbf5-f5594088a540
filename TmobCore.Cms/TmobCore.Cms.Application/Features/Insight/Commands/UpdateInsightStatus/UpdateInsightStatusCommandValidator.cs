using FluentValidation;
using TmobCore.Cms.Application.Contracts.Persistence;

namespace TmobCore.Cms.Application.Features.Insight.Commands.UpdateInsightStatus;
public class UpdateInsightStatusCommandValidator : AbstractValidator<UpdateInsightStatusCommand>
{
    private readonly IInsightRepository _insightRepository;

    public UpdateInsightStatusCommandValidator(IInsightRepository insightRepository)
    {
        _insightRepository = insightRepository;

        RuleFor(p => p.InsightGroupIds)
            .NotEmpty().WithMessage("InsightGroupIds cannot be empty")
            .Must(x => x != null && x.Any()).WithMessage("At least one InsightGroupId must be provided");

        RuleFor(p => p.Status)
            .IsInEnum().WithMessage("Invalid status value");
    }
}
