using FluentValidation;
using TmobCore.Cms.Application.Contracts.Persistence;

namespace TmobCore.Cms.Application.Features.Insight.Commands.DeleteInsight
{
    public class DeleteInsightsByIdCommandValidator : AbstractValidator<DeleteInsightsByIdCommand>
    {
        private readonly IInsightRepository _insightRepository;

        public DeleteInsightsByIdCommandValidator(IInsightRepository insightRepository)
        {
            _insightRepository = insightRepository;

            RuleFor(p => p.Ids)
                .NotEmpty().WithMessage("Insight IDs cannot be empty")
                .Must(x => x != null && x.Any()).WithMessage("At least one Insight ID must be provided");

            RuleForEach(p => p.Ids)
                .NotEmpty().WithMessage("Insight ID cannot be empty")
                .MustAsync(async (id, cancellation) => await InsightExists(id))
                .WithMessage("Insight with ID {PropertyValue} does not exist");
        }

        private async Task<bool> InsightExists(Guid id)
        {
            var insight = await _insightRepository.GetByIdAsync(id);
            return insight != null && !insight.Deleted;
        }
    }
}
