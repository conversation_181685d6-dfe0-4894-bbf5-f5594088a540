using MediatR;
using TmobCore.Cms.Application.Contracts.Logging;
using TmobCore.Cms.Application.Contracts.Persistence;
using TmobCore.Cms.Application.Models.Common;

namespace TmobCore.Cms.Application.Features.Insight.Commands.DeleteInsight
{
    public class DeleteInsightsByIdCommandHandler : IRequestHandler<DeleteInsightsByIdCommand, ActionResponse<int>>
    {
        private readonly IInsightRepository _insightRepository;
        private readonly IAppLogger<DeleteInsightsByIdCommandHandler> _logger;
        private readonly IUnitOfWork _uow;

        public DeleteInsightsByIdCommandHandler(IInsightRepository insightRepository,
                                              IAppLogger<DeleteInsightsByIdCommandHandler> logger,
                                              IUnitOfWork uow)
        {
            _insightRepository = insightRepository;
            _logger = logger;
            _uow = uow;
        }

        public async Task<ActionResponse<int>> Handle(DeleteInsightsByIdCommand request, CancellationToken cancellationToken)
        {
            try
            {
                // Validate the delete request
                var validator = new DeleteInsightsByIdCommandValidator(_insightRepository);
                var validationResult = await validator.ValidateAsync(request, cancellationToken);

                if (!validationResult.IsValid)
                {
                    var validationErrors = string.Join(", ", validationResult.Errors.Select(x => x.ErrorMessage));
                    _logger.LogWarning($"Insights deletion failed due to validation errors: {validationErrors}");
                    return ActionResponse<int>.Fail(validationErrors, StatusCode.BadRequest);
                }

                var deletedCount = 0;
                var invalidIds = new List<Guid>();

                foreach (var id in request.Ids)
                {
                    var insight = await _insightRepository.GetByIdAsync(id);
                    if (insight != null && !insight.Deleted)
                    {
                        insight.Deleted = true;
                        _insightRepository.Update(insight);
                        deletedCount++;
                    }
                    else
                    {
                        invalidIds.Add(id);
                    }
                }

                if (invalidIds.Any())
                {
                    _logger.LogWarning($"Some insight IDs were invalid: {string.Join(", ", invalidIds)}");
                    return ActionResponse<int>.Fail($"Invalid insight IDs: {string.Join(", ", invalidIds)}", StatusCode.BadRequest);
                }

                if (deletedCount == 0)
                {
                    _logger.LogWarning("No insights found to delete", request.Ids);
                    return ActionResponse<int>.Fail("No insights found to delete", StatusCode.NotFound);
                }

                await _uow.SaveChangesAsync(cancellationToken);
                _logger.LogInformation($"{deletedCount} insights have been deleted successfully", request.Ids);

                return ActionResponse<int>.Success(deletedCount, StatusCode.Ok);
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"An error occurred while deleting insights - {ex.Message}", request.Ids);
                return ActionResponse<int>.Fail($"An error occurred while deleting insights. Error: {ex.Message}", StatusCode.BadRequest);
            }
        }
    }
}
