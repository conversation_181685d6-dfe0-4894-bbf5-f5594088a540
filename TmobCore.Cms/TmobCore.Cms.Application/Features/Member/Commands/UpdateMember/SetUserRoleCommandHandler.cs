using MediatR;
using Microsoft.AspNetCore.Identity;
using TmobCore.Cms.Application.Contracts.Logging;
using TmobCore.Cms.Application.Contracts.Persistence;
using TmobCore.Cms.Application.Contracts.User;
using TmobCore.Cms.Application.Models.Common;
using TmobCore.Cms.Domain.Entities;

namespace TmobCore.Cms.Application.Features.Member.Commands.UpdateMember
{
    public class SetUserRoleCommandHandler : IRequestHandler<SetUserRoleCommand, ActionResponse<bool>>
    {
        private readonly IMemberRepository _memberRepository;
        private readonly IAppLogger<SetUserRoleCommandHandler> _logger;
        private readonly UserManager<User> _userManager;
        private readonly IUserPrincipal _userPrincipal;
        private readonly IUnitOfWork _uow;
        public SetUserRoleCommandHandler(IMemberRepository memberRepository,
                                              IAppLogger<SetUserRoleCommandHandler> logger,
                                              IUserPrincipal userPrincipal,
                                              UserManager<User> userManager,
                                              IUnitOfWork uow)
        {
            _memberRepository = memberRepository;
            _logger = logger;
            _userPrincipal = userPrincipal;
            _userManager = userManager;
            _uow = uow;
        }
        public async Task<ActionResponse<bool>> Handle(SetUserRoleCommand request, CancellationToken cancellationToken)
        {
            using (var transaction = _uow.BeginTransaction())
            {
                try
                {
                    var user = await _userManager.FindByEmailAsync(request.Email);
                    if (user != null && !String.IsNullOrEmpty(_userPrincipal.ProjectId))
                    {
                        var validator = new SetUserRoleCommandValidator(_memberRepository);
                        var validationResult = await validator.ValidateAsync(request);

                        if (!validationResult.IsValid)
                        {
                            _logger.LogWarning($"User Role update failed due to validation errors - {request}");
                            return ActionResponse<bool>.Fail($"User Role update failed due to validation errors - {request}", StatusCode.BadRequest);
                        }

                        var userRole = await _userManager.GetRolesAsync(user);
                        if (userRole != null)
                        {
                            await _userManager.RemoveFromRoleAsync(user, userRole.FirstOrDefault());
                        }

                        var userProjectRole = await _memberRepository.GetUserProjectRole(user.Id, _userPrincipal.ProjectId);

                        if (userProjectRole != null)
                        {
                            // Mevcut kaydı sil
                            _memberRepository.Delete(userProjectRole);
                            await _uow.SaveChangesAsync();

                            // Yeni rolü al
                            var memberRole = await _memberRepository.GetMemberRoleById(request.RoleId);
                            await _userManager.AddToRoleAsync(user, memberRole.Name);
                            var role = await _memberRepository.GetUserRoleByRoleName(memberRole.Name);

                            // Yeni UserProjectRole nesnesi oluştur
                            var newUserProjectRole = new UserProjectRole
                            {
                                UserId = user.Id,
                                ProjectId = Guid.Parse(_userPrincipal.ProjectId),
                                Role = role,
                                Status = request.Status
                            };

                            // Yeni nesneyi veritabanına ekle
                            await _memberRepository.CreateAsync(newUserProjectRole);
                            await _uow.SaveChangesAsync();
                        }
                    }
                    _logger.LogInformation("User role is set", request.Email);
                    transaction.Commit();
                    return ActionResponse<bool>.Success(true, StatusCode.Ok);
                }
                catch (Exception ex)
                {
                    transaction.Rollback();
                    _logger.LogWarning("User role cannot set", request.Email, ex);
                    return ActionResponse<bool>.Fail(ex.ToString(), StatusCode.InternalServerError);
                }
            }
        }

    }
}
