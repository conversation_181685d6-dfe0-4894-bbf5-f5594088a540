using AutoMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using TmobCore.Cms.Application.Contracts.Logging;
using TmobCore.Cms.Application.Contracts.Persistence;
using TmobCore.Cms.Application.Models.Campaign;
using TmobCore.Cms.Application.Models.Common;

namespace TmobCore.Cms.Application.Features.Campaigns.Queries.GetCampaignById;

public class GetCampaignByIdQueryHandler : IRequestHandler<GetCampaignByIdQuery, ActionResponse<CampaignResponse>>
{
    private readonly ICampaignRepository _campaignRepository;
    private readonly IAppLogger<GetCampaignByIdQueryHandler> _logger;
    private readonly IMapper _mapper;

    public GetCampaignByIdQueryHandler(IMapper mapper, ICampaignRepository campaignRepository,
        IAppLogger<GetCampaignByIdQueryHandler> logger)
    {
        _mapper = mapper;
        _campaignRepository = campaignRepository;
        _logger = logger;
    }

    public async Task<ActionResponse<CampaignResponse>> Handle(GetCampaignByIdQuery request,
        CancellationToken cancellationToken)
    {
        try
        {
            //Query the database
            var campaign = await _campaignRepository
                .GetQuery(x => x.Id == request.Id && !x.Deleted, x => x.CampaignType, i =>i.Image).FirstOrDefaultAsync();

            if (campaign != null)
            {
                //convert data objects to DTO objects
                var data = _mapper.Map<CampaignResponse>(campaign);

                //return list of DTO object
                _logger.LogInformation($"Campaing found successfully for request id {request.Id}.");
                return ActionResponse<CampaignResponse>.Success(data, StatusCode.Ok);
            }

            _logger.LogWarning($"Campaign not found or deleted for this id {request.Id}");
            return ActionResponse<CampaignResponse>.Fail($"Campaign not found for this id {request.Id}",
                StatusCode.NotFound);
        }
        catch (Exception ex)
        {
            _logger.LogWarning("GetCampaignByIdQueryHandler => Campaign not found", request, ex);
            return ActionResponse<CampaignResponse>.Fail($"Campaign not returned.Response: {ex}",
                StatusCode.BadRequest);
        }
    }
}