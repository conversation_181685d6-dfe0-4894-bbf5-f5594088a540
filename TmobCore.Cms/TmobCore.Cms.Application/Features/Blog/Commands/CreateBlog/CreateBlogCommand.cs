using MediatR;
using TmobCore.Cms.Application.Models.Blog;
using TmobCore.Cms.Application.Models.Common;

namespace TmobCore.Cms.Application.Features.Blog.Commands.CreateBlog
{
    public class CreateBlogCommand : IRequest<ActionResponse<bool>>
    {
        public List<CreateBlogRequest> Blog { get; set; }
        
    }

    public class CreateBlogRequest : BaseRequest
    {
        public string Title { get; set; }
        public string Slug { get; set; }
        public DateTime BeginDate { get; set; }
        public DateTime EndDate { get; set; }
        public Guid? ImageId { get; set; }
        
        public string Tags { get; set; } = string.Empty;
        
        public int ReadingTime { get; set; } = 0;
        
        public string? CardTitle { get; set; }           
        
        public string? CardDescription { get; set; }  
        
        public string? AirportCode { get; set; }

        public bool Visible { get; set; }

        public bool IsProfileCampaign { get; set; } = false;

        public List<BlogContentRequest> BlogContents { get; set; } = new();
        public List<Guid> FilterIds { get; set; } = new();
    }
}
