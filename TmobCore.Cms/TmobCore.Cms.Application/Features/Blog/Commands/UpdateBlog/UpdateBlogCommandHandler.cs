using MediatR;
using Microsoft.EntityFrameworkCore;
using TmobCore.Cms.Application.Contracts.Logging;
using TmobCore.Cms.Application.Contracts.Persistence;
using TmobCore.Cms.Application.Contracts.User;
using TmobCore.Cms.Application.Helper;
using TmobCore.Cms.Application.Models.Common;
using TmobCore.Cms.Domain.Entities;

namespace TmobCore.Cms.Application.Features.Blog.Commands.UpdateBlog
{
    public class UpdateBlogCommandHandler : IRequestHandler<UpdateBlogCommand, ActionResponse<bool>>
    {
        private readonly IBlogRepository _blogRepository;
        private readonly IAppLogger<UpdateBlogCommandHandler> _logger;
        private readonly IUserPrincipal _userPrincipal;
        private readonly IUnitOfWork _uow;

        public UpdateBlogCommandHandler(IBlogRepository blogRepository,
                                 IAppLogger<UpdateBlogCommandHandler> logger,
                                 IUserPrincipal userPrincipal,
                                 IUnitOfWork uow)
        {
            _blogRepository = blogRepository;
            _userPrincipal = userPrincipal;
            _logger = logger;
            _uow = uow;
        }

        public async Task<ActionResponse<bool>> Handle(UpdateBlogCommand request, CancellationToken cancellationToken)
        {
            try
            {
                foreach (var blogRequest in request.Blogs)
                {
                    var blog = await _blogRepository
                        .GetQuery(x => x.Id == blogRequest.Id, y => y.BlogContents, z => z.BlogBlogFilters)
                        .FirstOrDefaultAsync(cancellationToken);

                    if (blog == null)
                    {
                        _logger.LogWarning($"Blog not found with id: {blogRequest.Id}");
                        continue;
                    }
                    
                    blog.Status = (Domain.Common.BaseStatus)blogRequest.Status;
                    blog.Tags = blogRequest.Tags;
                    blog.Title = blogRequest.Title;
                    blog.Slug = blogRequest.Slug;
                    blog.BeginDate = blogRequest.BeginDate;
                    blog.EndDate = blogRequest.EndDate;
                    blog.ReadingTime = blogRequest.ReadingTime;
                    blog.ImageId = FileExtentions.HandleFileIdAssignment(blogRequest.ImageId,blog.ImageId);

                    // Update card-related properties
                    blog.CardTitle = blogRequest.CardTitle;
                    blog.CardDescription = blogRequest.CardDescription;
                    blog.AirportCode = blogRequest.AirportCode;
                    blog.Visible = blogRequest.Visible;
                    blog.IsProfileCampaign = blogRequest.IsProfileCampaign;
                    if (blogRequest.FilterIds != null && blogRequest.FilterIds.Any())
                    {
                        blog.BlogBlogFilters.Clear();
                        foreach (var requestFilterId in blogRequest.FilterIds)
                        {
                            blog.BlogBlogFilters.Add(new BlogBlogFilter
                            {
                                BlogFilterId = requestFilterId
                            });
                        }
                    }
                    else
                    {
                        blog.BlogBlogFilters.Clear();
                    }

                    if (blogRequest.BlogContents.Any())
                    {
                        foreach(var data in blogRequest.BlogContents)
                        {
                            var blogContent = blog.BlogContents.FirstOrDefault(x => x.Id == data.Id);
                            if (blogContent != null)
                            {
                                blogContent.Content = data.Content;
                                blogContent.Order = data.Order;
                                blogContent.FileId = FileExtentions.HandleFileIdAssignment(data.FileId,blogContent.FileId);
                            }
                        }
                    }

                    _blogRepository.Update(blog);
                }

                await _uow.SaveChangesAsync(cancellationToken);
                _logger.LogInformation("Blogs updated successfully");
                return ActionResponse<bool>.Success(true, StatusCode.Ok);
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"Blog update failed due to exception: {ex}");
                return ActionResponse<bool>.Fail(ex.ToString(), StatusCode.BadRequest);
            }
        }
    }
}