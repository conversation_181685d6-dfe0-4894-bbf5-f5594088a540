using MediatR;
using TmobCore.Cms.Application.Models.Blog;
using TmobCore.Cms.Application.Models.Common;

namespace TmobCore.Cms.Application.Features.Blog.Commands.UpdateBlog
{
    public class UpdateBlogCommand : IRequest<ActionResponse<bool>>
    {
        public List<UpdateBlogRequest> Blogs { get; set; } = new();
    }

    public class UpdateBlogRequest : BaseRequest
    {
        public Guid Id { get; set; }
        public string Title { get; set; }
        public string Slug { get; set; }
        public DateTime BeginDate { get; set; }
        public DateTime EndDate { get; set; }
        
        public Guid? ImageId { get; set; }
        public string Tags { get; set; } = string.Empty;
        public int ReadingTime { get; set; } = 0;
        
        public string? CardTitle { get; set; }
        public string? CardDescription { get; set; }
        public string? AirportCode { get; set; }
        public bool Visible { get; set; }
        public bool IsProfileCampaign { get; set; }
        
        public List<BlogContentUpdateRequest> BlogContents { get; set; } = new();
        public List<Guid> FilterIds { get; set; } = new();
    }
}
