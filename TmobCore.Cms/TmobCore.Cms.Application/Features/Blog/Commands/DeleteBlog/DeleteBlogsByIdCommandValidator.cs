using FluentValidation;
using TmobCore.Cms.Application.Contracts.Persistence;

namespace TmobCore.Cms.Application.Features.Blog.Commands.DeleteBlog
{
    public class DeleteBlogsByIdCommandValidator : AbstractValidator<DeleteBlogsByIdCommand>
    {
        private readonly IBlogRepository _blogRepository;

        public DeleteBlogsByIdCommandValidator(IBlogRepository blogRepository)
        {
            _blogRepository = blogRepository;

            RuleFor(p => p.Ids)
                .NotEmpty().WithMessage("Blog IDs cannot be empty")
                .Must(x => x != null && x.Any()).WithMessage("At least one Blog ID must be provided");

            RuleForEach(p => p.Ids)
                .NotEmpty().WithMessage("Blog ID cannot be empty")
                .MustAsync(async (id, cancellation) => await BlogExists(id))
                .WithMessage("Blog with ID {PropertyValue} does not exist");
        }

        private async Task<bool> BlogExists(Guid id)
        {
            var blog = await _blogRepository.GetByIdAsync(id);
            return blog != null && !blog.Deleted;
        }
    }
}
