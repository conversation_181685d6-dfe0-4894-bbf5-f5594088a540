using MediatR;
using TmobCore.Cms.Application.Contracts.Logging;
using TmobCore.Cms.Application.Contracts.Persistence;
using TmobCore.Cms.Application.Models.Common;

namespace TmobCore.Cms.Application.Features.Blog.Commands.DeleteBlog
{
    public class DeleteBlogsByIdCommandHandler : IRequestHandler<DeleteBlogsByIdCommand, ActionResponse<int>>
    {
        private readonly IBlogRepository _blogRepository;
        private readonly IAppLogger<DeleteBlogsByIdCommandHandler> _logger;
        private readonly IUnitOfWork _uow;

        public DeleteBlogsByIdCommandHandler(IBlogRepository blogRepository,
                                           IAppLogger<DeleteBlogsByIdCommandHandler> logger,
                                           IUnitOfWork uow)
        {
            _blogRepository = blogRepository;
            _logger = logger;
            _uow = uow;
        }

        public async Task<ActionResponse<int>> Handle(DeleteBlogsByIdCommand request, CancellationToken cancellationToken)
        {
            try
            {
                // Validate the delete request
                var validator = new DeleteBlogsByIdCommandValidator(_blogRepository);
                var validationResult = await validator.ValidateAsync(request, cancellationToken);

                if (!validationResult.IsValid)
                {
                    var validationErrors = string.Join(", ", validationResult.Errors.Select(x => x.ErrorMessage));
                    _logger.LogWarning($"Blogs deletion failed due to validation errors: {validationErrors}");
                    return ActionResponse<int>.Fail(validationErrors, StatusCode.BadRequest);
                }

                var deletedCount = 0;
                var invalidIds = new List<Guid>();

                foreach (var id in request.Ids)
                {
                    var blog = await _blogRepository.GetByIdAsync(id);
                    if (blog != null && !blog.Deleted)
                    {
                        blog.Deleted = true;
                        _blogRepository.Update(blog);
                        deletedCount++;
                    }
                    else
                    {
                        invalidIds.Add(id);
                    }
                }

                if (invalidIds.Any())
                {
                    _logger.LogWarning($"Some blog IDs were invalid: {string.Join(", ", invalidIds)}");
                    return ActionResponse<int>.Fail($"Invalid blog IDs: {string.Join(", ", invalidIds)}", StatusCode.BadRequest);
                }

                if (deletedCount == 0)
                {
                    _logger.LogWarning("No blogs found to delete", request.Ids);
                    return ActionResponse<int>.Fail("No blogs found to delete", StatusCode.NotFound);
                }

                await _uow.SaveChangesAsync(cancellationToken);
                _logger.LogInformation($"{deletedCount} blogs have been deleted successfully", request.Ids);

                return ActionResponse<int>.Success(deletedCount, StatusCode.Ok);
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"An error occurred while deleting blogs - {ex.Message}", request.Ids);
                return ActionResponse<int>.Fail($"An error occurred while deleting blogs. Error: {ex.Message}", StatusCode.BadRequest);
            }
        }
    }
}
