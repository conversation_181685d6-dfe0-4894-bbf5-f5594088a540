using MediatR;
using TmobCore.Cms.Application.Contracts.Logging;
using TmobCore.Cms.Application.Contracts.Persistence;
using TmobCore.Cms.Application.Models.Common;

namespace TmobCore.Cms.Application.Features.Blog.Commands.DeleteBlogFilter
{
    public class DeleteBlogFiltersByIdCommandHandler : IRequestHandler<DeleteBlogFiltersByIdCommand, ActionResponse<int>>
    {
        private readonly IBlogFilterRepository _blogFilterRepository;
        private readonly IAppLogger<DeleteBlogFiltersByIdCommandHandler> _logger;
        private readonly IUnitOfWork _uow;

        public DeleteBlogFiltersByIdCommandHandler(IBlogFilterRepository blogFilterRepository,
                                                IAppLogger<DeleteBlogFiltersByIdCommandHandler> logger,
                                                IUnitOfWork uow)
        {
            _blogFilterRepository = blogFilterRepository;
            _logger = logger;
            _uow = uow;
        }

        public async Task<ActionResponse<int>> Handle(DeleteBlogFiltersByIdCommand request, CancellationToken cancellationToken)
        {
            try
            {
                // Validate the delete request
                var validator = new DeleteBlogFiltersByIdCommandValidator(_blogFilterRepository);
                var validationResult = await validator.ValidateAsync(request, cancellationToken);

                if (!validationResult.IsValid)
                {
                    var validationErrors = string.Join(", ", validationResult.Errors.Select(x => x.ErrorMessage));
                    _logger.LogWarning($"Blog filters deletion failed due to validation errors: {validationErrors}");
                    return ActionResponse<int>.Fail(validationErrors, StatusCode.BadRequest);
                }

                var deletedCount = 0;
                var invalidIds = new List<Guid>();

                foreach (var id in request.Ids)
                {
                    var blogFilter = await _blogFilterRepository.GetByIdAsync(id);
                    if (blogFilter != null && !blogFilter.Deleted)
                    {
                        blogFilter.Deleted = true;
                        _blogFilterRepository.Update(blogFilter);
                        deletedCount++;
                    }
                    else
                    {
                        invalidIds.Add(id);
                    }
                }

                if (invalidIds.Any())
                {
                    _logger.LogWarning($"Some blog filter IDs were invalid: {string.Join(", ", invalidIds)}");
                    return ActionResponse<int>.Fail($"Invalid blog filter IDs: {string.Join(", ", invalidIds)}", StatusCode.BadRequest);
                }

                if (deletedCount == 0)
                {
                    _logger.LogWarning("No blog filters found to delete", request.Ids);
                    return ActionResponse<int>.Fail("No blog filters found to delete", StatusCode.NotFound);
                }

                await _uow.SaveChangesAsync(cancellationToken);
                _logger.LogInformation($"{deletedCount} blog filters have been deleted successfully", request.Ids);

                return ActionResponse<int>.Success(deletedCount, StatusCode.Ok);
            }
            catch (Exception ex)
            {
                _logger.LogWarning("Blog filters deletion failed", request.Ids, ex);
                return ActionResponse<int>.Fail($"An error occurred while deleting blog filters. Error Message: {ex.Message}", StatusCode.BadRequest);
            }
        }
    }
}
