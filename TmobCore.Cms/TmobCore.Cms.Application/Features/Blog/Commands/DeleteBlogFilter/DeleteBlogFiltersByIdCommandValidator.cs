using FluentValidation;
using TmobCore.Cms.Application.Contracts.Persistence;

namespace TmobCore.Cms.Application.Features.Blog.Commands.DeleteBlogFilter
{
    public class DeleteBlogFiltersByIdCommandValidator : AbstractValidator<DeleteBlogFiltersByIdCommand>
    {
        private readonly IBlogFilterRepository _blogFilterRepository;

        public DeleteBlogFiltersByIdCommandValidator(IBlogFilterRepository blogFilterRepository)
        {
            _blogFilterRepository = blogFilterRepository;

            RuleFor(p => p.Ids)
                .NotEmpty().WithMessage("Blog Filter IDs cannot be empty")
                .Must(x => x != null && x.Any()).WithMessage("At least one Blog Filter ID must be provided");

            RuleForEach(p => p.Ids)
                .NotEmpty().WithMessage("Blog Filter ID cannot be empty")
                .MustAsync(async (id, cancellation) => await BlogFilterExists(id))
                .WithMessage("Blog Filter with ID {PropertyValue} does not exist");
        }

        private async Task<bool> BlogFilterExists(Guid id)
        {
            var blogFilter = await _blogFilterRepository.GetByIdAsync(id);
            return blogFilter != null && !blogFilter.Deleted;
        }
    }
}
