using AutoMapper;
using MediatR;
using TmobCore.Cms.Application.Contracts.Logging;
using TmobCore.Cms.Application.Contracts.Persistence;
using TmobCore.Cms.Application.Contracts.User;
using TmobCore.Cms.Application.Helper;
using TmobCore.Cms.Application.Models.Blog;
using TmobCore.Cms.Application.Models.Common;

namespace TmobCore.Cms.Application.Features.Blog.Queries.GetBlogs
{
    public class GetBlogsQueryHandler : IRequestHandler<GetBlogsQuery, ActionResponse<List<BlogResponse>>>
    {
        private readonly IBlogRepository _blogRepository;
        private readonly IUserPrincipal _userPrincipal;
        private readonly IMapper _mapper;
        private readonly IAppLogger<GetBlogsQueryHandler> _logger;
        public GetBlogsQueryHandler(IBlogRepository blogRepository,
                                    IMapper mapper,
                                    IUserPrincipal userPrincipal,
                                    IAppLogger<GetBlogsQueryHandler> logger)
        {
            _blogRepository = blogRepository;
            _userPrincipal = userPrincipal;
            _mapper = mapper;
            _logger = logger;
        }
        public async Task<ActionResponse<List<BlogResponse>>> Handle(GetBlogsQuery request, CancellationToken cancellationToken)
        {
            try
            {
                var blogsRequest = new BlogRequest
                {
                    ProjectId = _userPrincipal.ProjectId.ToGuid(),
                    SearchTerm = request.SearchTerm,
                    FilterIds = request.FilterIds,
                    Page = request.Page,
                    PageSize = request.PageSize,
                    GroupId = request.GroupId,
                    IsProfileCampaign = request.IsProfileCampaign
                };

                var (blogs, totalCount) = await _blogRepository.GetBlogs(blogsRequest, cancellationToken);
                var blogsResponse = _mapper.Map<List<BlogResponse>>(blogs);

                return ActionResponse<List<BlogResponse>>.Success(blogsResponse, StatusCode.Ok)
                    .WithPagination(request.Page, request.PageSize, totalCount);
            }
            catch (Exception ex)
            {
                _logger.LogWarning("GetBlogsQueryHandler => Blogs not returned", request, ex);
                return ActionResponse<List<BlogResponse>>.Fail($"Blogs not returned.Request: {request} Response: {ex.ToString()}", StatusCode.BadRequest);
            }
        }
    }
}
