using MediatR;
using TmobCore.Cms.Application.Models.Blog;
using TmobCore.Cms.Application.Models.Common;

namespace TmobCore.Cms.Application.Features.Blog.Queries.GetBlogs
{
    public class GetBlogsQuery : IRequest<ActionResponse<List<BlogResponse>>>
    {
        public string? SearchTerm { get; set; }
        public List<Guid> FilterIds { get; set; } = new();
        public int Page { get; set; } = 1;
        public int PageSize { get; set; } = 10000;
        public Guid? GroupId { get; set; }
        public bool? IsProfileCampaign { get; set; }
    }
}
