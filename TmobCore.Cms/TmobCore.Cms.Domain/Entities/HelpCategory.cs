using TmobCore.Cms.Domain.Common;

namespace TmobCore.Cms.Domain.Entities
{
    public class HelpCategory : IdBaseEntity
    {
        public string Title { get; set; } = null!;
        public string? Description { get; set; }

        public Guid? ParentCategoryId { get; set; }
        public string? Icon { get; set; }
        public HelpCategory? ParentCategory { get; set; }
        public ICollection<HelpCategory> SubCategories { get; set; } = new List<HelpCategory>();

        public ICollection<HelpArticle> Articles { get; set; } = new List<HelpArticle>();
    }
}