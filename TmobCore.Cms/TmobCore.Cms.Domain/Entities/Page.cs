using TmobCore.Cms.Domain.Common;

namespace TmobCore.Cms.Domain.Entities
{
    public class Page : BaseEntityExtended
    {
        public Guid ProjectId { get; set; }
        public Guid UserId { get; set; }
         public Guid GroupId { get; set; }
        public Guid? ParentPageId { get; set; }
        public string Title { get; set; }
        public string Slug { get; set; }
        public string Type { get; set; } = string.Empty;
        public string Icon { get; set; } = string.Empty;
        public int Order { get; set; }
        public DateTime PublishDate { get; set; } = DateTime.Now;
        public Page ParentPage { get; set; }
        public List<Page> SubPages { get; set; }
        public List<PageContent> PageContents { get; set; }
        public Guid? ImageId { get; set; }
        public virtual Image Image { get; set; }
    }
}