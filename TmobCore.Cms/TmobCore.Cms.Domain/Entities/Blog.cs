using TmobCore.Cms.Domain.Common;

namespace TmobCore.Cms.Domain.Entities
{
    public class Blog : BaseEntityExtended
    {
        public Guid UserId { get; set; }
        public Guid ProjectId { get; set; }
        public Guid GroupId { get; set; }
        
        public string Title { get; set; }
        public string Slug { get; set; }
        public DateTime BeginDate { get; set; }
        public DateTime EndDate { get; set; }
        public Guid? ImageId { get; set; }
        public string Tags { get; set; } = string.Empty;
        public int ReadingTime { get; set; }
        
        // Flight Tickets for Destinations Card
        public string? CardTitle { get; set; }
        public string? CardDescription { get; set; }
        public string? AirportCode { get; set; }
        public bool Visible { get; set; } = true;

        // Profile Campaign Flag
        public bool IsProfileCampaign { get; set; } = false;
        
        public List<BlogContent> BlogContents { get; set; }
        public virtual List<BlogBlogFilter> BlogBlogFilters { get; set; }
        
        public virtual Image Image { get; set; }
        
    }
}
